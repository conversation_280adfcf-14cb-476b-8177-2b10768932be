<template>
  <div class="about">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="app-logo">
        <font-awesome-icon icon="cogs" class="logo-icon" />
      </div>
      <h1>TabKit</h1>
      <p class="version">版本 1.0.0</p>
    </div>

    <!-- 应用信息 -->
    <div class="app-info">
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <font-awesome-icon icon="info-circle" />
            <span>应用信息</span>
          </div>
        </template>

        <div class="info-content">
          <p class="description">
            Alsi.Tab.Kit 是一个集成多种实用工具的桌面应用程序，旨在提高工作效率和简化日常任务。
          </p>
        </div>
      </el-card>

      <!-- 技术信息 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <font-awesome-icon icon="code" />
            <span>技术信息</span>
          </div>
        </template>

        <div class="tech-info">
          <div class="tech-grid">
            <div class="tech-item">
              <span class="tech-label">前端框架:</span>
              <span class="tech-value">Vue 3 + TypeScript</span>
            </div>
            <div class="tech-item">
              <span class="tech-label">UI组件库:</span>
              <span class="tech-value">Element Plus</span>
            </div>
            <div class="tech-item">
              <span class="tech-label">图标库:</span>
              <span class="tech-value">FontAwesome</span>
            </div>
            <div class="tech-item">
              <span class="tech-label">后端框架:</span>
              <span class="tech-value">.NET Framework</span>
            </div>
            <div class="tech-item">
              <span class="tech-label">桌面框架:</span>
              <span class="tech-value">WPF + WebView2</span>
            </div>
            <div class="tech-item">
              <span class="tech-label">构建工具:</span>
              <span class="tech-value">Vue CLI + Webpack</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 联系信息 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <font-awesome-icon icon="envelope" />
            <span>联系信息</span>
          </div>
        </template>

        <div class="contact-item">
          <span>如果您在使用过程中遇到问题或有任何建议，欢迎联系：</span>
          <font-awesome-icon icon="envelope" class="contact-icon" />
          <a href="mailto:<EMAIL>" class="contact-link"><EMAIL></a>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

export default defineComponent({
  name: "AboutView",
  components: {
    FontAwesomeIcon,
  },
});
</script>

<style scoped>
.about {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.app-logo {
  margin-bottom: 20px;
}

.logo-icon {
  font-size: 4rem;
  color: var(--el-color-primary);
}

.page-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 10px;
}

.version {
  color: #7f8c8d;
  font-size: 1.1rem;
  margin: 0;
}

.app-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 40px;
}

.info-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
  color: #2c3e50;
  font-size: 1.1rem;
}

.info-content {
  line-height: 1.6;
}

.description {
  color: #555;
  font-size: 1rem;
  margin-bottom: 25px;
}

.features h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.2rem;
}

.features ul {
  list-style: none;
  padding: 0;
}

.features li {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.feature-icon {
  color: #3498db;
  margin-top: 2px;
  flex-shrink: 0;
}

.features li strong {
  color: #2c3e50;
}

.tech-info {
  padding: 10px 0;
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.tech-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.tech-label {
  color: #7f8c8d;
  font-weight: 500;
}

.tech-value {
  color: #2c3e50;
  font-weight: bold;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.contact-icon {
  color: var(--el-color-primary);
  width: 16px;
}

.contact-link {
  color: var(--el-color-primary);
  text-decoration: none;
  transition: color 0.3s ease;
}

.contact-link:hover {
  color: var(--el-color-primary-dark-1);
  text-decoration: underline;
}



@media (max-width: 768px) {
  .about {
    padding: 15px;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .logo-icon {
    font-size: 3rem;
  }

  .tech-grid {
    grid-template-columns: 1fr;
  }

  .tech-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .features li {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
