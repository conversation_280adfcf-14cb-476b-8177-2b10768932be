{"Version": 1, "WorkspaceRootPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{5F059B1D-FA34-4752-8DAE-0B78E6DDF378}|..\\Alsi.Common\\Alsi.Common.Log\\Alsi.Common.Log.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.common\\alsi.common.log\\frames\\canfdflags.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5F059B1D-FA34-4752-8DAE-0B78E6DDF378}|..\\Alsi.Common\\Alsi.Common.Log\\Alsi.Common.Log.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.common\\alsi.common.log\\frames\\canfdframe.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5F059B1D-FA34-4752-8DAE-0B78E6DDF378}|..\\Alsi.Common\\Alsi.Common.Log\\Alsi.Common.Log.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.common\\alsi.common.log\\frames\\canframe.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5F059B1D-FA34-4752-8DAE-0B78E6DDF378}|..\\Alsi.Common\\Alsi.Common.Log\\Alsi.Common.Log.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.common\\alsi.common.log\\frames\\linframe.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5F059B1D-FA34-4752-8DAE-0B78E6DDF378}|..\\Alsi.Common\\Alsi.Common.Log\\Alsi.Common.Log.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.common\\alsi.common.log\\frames\\tabframe.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.tab.kit\\alsi.tab.kit.core\\services\\datalogconvertservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|solutionrelative:alsi.tab.kit.core\\services\\datalogconvertservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\build-package.ps1||{3B902123-F8A7-4915-9F01-361F908088D0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:build-package.ps1||{3B902123-F8A7-4915-9F01-361F908088D0}"}, {"AbsoluteMoniker": "D:0:0:{9F3C92DC-E277-4374-B637-7C6DF32FCEB0}|..\\Alsi.Common\\Alsi.App.Edge\\Alsi.App.Edge.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.common\\alsi.app.edge\\views\\edgebrowser.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{6324226f-61b6-4f28-92ee-18d4b5fe1e48}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "CanFdFlags.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Log\\Frames\\CanFdFlags.cs", "RelativeDocumentMoniker": "..\\Alsi.Common\\Alsi.Common.Log\\Frames\\CanFdFlags.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Log\\Frames\\CanFdFlags.cs", "RelativeToolTip": "..\\Alsi.Common\\Alsi.Common.Log\\Frames\\CanFdFlags.cs", "ViewState": "AgIAABsAAAAAAAAAAAAAABsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T08:30:23.806Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "TabFrame.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Log\\Frames\\TabFrame.cs", "RelativeDocumentMoniker": "..\\Alsi.Common\\Alsi.Common.Log\\Frames\\TabFrame.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Log\\Frames\\TabFrame.cs", "RelativeToolTip": "..\\Alsi.Common\\Alsi.Common.Log\\Frames\\TabFrame.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAADIAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T08:29:54.641Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "CanFdFrame.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Log\\Frames\\CanFdFrame.cs", "RelativeDocumentMoniker": "..\\Alsi.Common\\Alsi.Common.Log\\Frames\\CanFdFrame.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Log\\Frames\\CanFdFrame.cs", "RelativeToolTip": "..\\Alsi.Common\\Alsi.Common.Log\\Frames\\CanFdFrame.cs", "ViewState": "AgIAAA0AAAAAAAAAAAAxwF4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T08:29:46.003Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "LinFrame.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Log\\Frames\\LinFrame.cs", "RelativeDocumentMoniker": "..\\Alsi.Common\\Alsi.Common.Log\\Frames\\LinFrame.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Log\\Frames\\LinFrame.cs", "RelativeToolTip": "..\\Alsi.Common\\Alsi.Common.Log\\Frames\\LinFrame.cs", "ViewState": "AgIAAEsAAAAAAAAAAAAAAAoAAAAHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T08:28:24.72Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "CanFrame.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Log\\Frames\\CanFrame.cs", "RelativeDocumentMoniker": "..\\Alsi.Common\\Alsi.Common.Log\\Frames\\CanFrame.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Log\\Frames\\CanFrame.cs", "RelativeToolTip": "..\\Alsi.Common\\Alsi.Common.Log\\Frames\\CanFrame.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAowDgAAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T07:54:28.896Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "EdgeBrowser.xaml.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Edge\\Views\\EdgeBrowser.xaml.cs", "RelativeDocumentMoniker": "..\\Alsi.Common\\Alsi.App.Edge\\Views\\EdgeBrowser.xaml.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Edge\\Views\\EdgeBrowser.xaml.cs", "RelativeToolTip": "..\\Alsi.Common\\Alsi.App.Edge\\Views\\EdgeBrowser.xaml.cs", "ViewState": "AgIAAAQAAAAAAAAAAAAwwBAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T07:04:04.221Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "build-package.ps1", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\build-package.ps1", "RelativeDocumentMoniker": "build-package.ps1", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\build-package.ps1", "RelativeToolTip": "build-package.ps1", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-06-20T07:01:45.22Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "DataLogConvertService.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\DataLogConvertService.cs", "RelativeDocumentMoniker": "Alsi.Tab.Kit.Core\\Services\\DataLogConvertService.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\DataLogConvertService.cs", "RelativeToolTip": "Alsi.Tab.Kit.Core\\Services\\DataLogConvertService.cs", "ViewState": "AgIAAP0AAAAAAAAAAAAcwAoBAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T06:52:39.529Z", "EditorCaption": ""}]}]}]}