{"version": 3, "targets": {".NETFramework,Version=v4.6.2": {"AutoMapper/8.1.1": {"type": "package", "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net461/AutoMapper.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net461/AutoMapper.dll": {"related": ".pdb;.xml"}}}, "BouncyCastle.Cryptography/2.2.1": {"type": "package", "compile": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}, "runtime": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}}, "Enums.NET/4.0.1": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}}, "MahApps.Metro.IconPacks.Material/4.11.0": {"type": "package", "compile": {"lib/net46/MahApps.Metro.IconPacks.Core.dll": {"related": ".pdb;.xml"}, "lib/net46/MahApps.Metro.IconPacks.Material.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net46/MahApps.Metro.IconPacks.Core.dll": {"related": ".pdb;.xml"}, "lib/net46/MahApps.Metro.IconPacks.Material.dll": {"related": ".pdb;.xml"}}}, "MathNet.Numerics.Signed/4.15.0": {"type": "package", "frameworkAssemblies": ["System", "System.Core", "System.Numerics", "System.Runtime.Serialization"], "compile": {"lib/net461/MathNet.Numerics.dll": {"related": ".xml"}}, "runtime": {"lib/net461/MathNet.Numerics.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Client/6.0.0": {"type": "package", "dependencies": {"Newtonsoft.Json": "13.0.1", "Newtonsoft.Json.Bson": "1.0.2", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["System.Net.Http"], "compile": {"lib/net45/System.Net.Http.Formatting.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Net.Http.Formatting.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Core/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Client": "6.0.0"}, "compile": {"lib/net45/System.Web.Http.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Owin/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Core": "5.3.0", "Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/System.Web.Http.Owin.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.Owin.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.OwinSelfHost/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Owin": "5.3.0", "Microsoft.Owin.Host.HttpListener": "4.2.2", "Microsoft.Owin.Hosting": "4.2.2"}}, "Microsoft.IO.RecyclableMemoryStream/2.3.2": {"type": "package", "dependencies": {"System.Memory": "4.5.5"}, "compile": {"lib/net462/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "Microsoft.Owin/4.2.2": {"type": "package", "dependencies": {"Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.dll": {"related": ".xml"}}}, "Microsoft.Owin.Host.HttpListener/4.2.2": {"type": "package", "dependencies": {"Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.Host.HttpListener.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.Host.HttpListener.dll": {"related": ".xml"}}}, "Microsoft.Owin.Hosting/4.2.2": {"type": "package", "dependencies": {"Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.Hosting.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "dependencies": {"Newtonsoft.Json": "12.0.1"}, "compile": {"lib/net45/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}}, "NPOI/2.6.2": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.2.1", "Enums.NET": "4.0.1", "MathNet.Numerics.Signed": "4.15.0", "Microsoft.IO.RecyclableMemoryStream": "2.3.2", "SharpZipLib": "1.3.3", "SixLabors.Fonts": "1.0.0", "SixLabors.ImageSharp": "2.1.4", "System.Configuration.ConfigurationManager": "6.0.0", "System.Security.Cryptography.Xml": "6.0.1"}, "compile": {"lib/netstandard2.0/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}}, "Ookii.Dialogs.Wpf/5.0.1": {"type": "package", "frameworkAssemblies": ["PresentationCore", "PresentationFramework", "System", "System.Core", "System.Design", "System.Drawing", "System.Security", "WindowsBase"], "compile": {"lib/net462/Ookii.Dialogs.Wpf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/Ookii.Dialogs.Wpf.dll": {"related": ".pdb;.xml"}}}, "Owin/1.0.0": {"type": "package", "compile": {"lib/net40/Owin.dll": {}}, "runtime": {"lib/net40/Owin.dll": {}}}, "Serilog/2.10.0": {"type": "package", "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Core"], "compile": {"lib/net46/Serilog.dll": {"related": ".xml"}}, "runtime": {"lib/net46/Serilog.dll": {"related": ".xml"}}}, "Serilog.Sinks.Console/4.1.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net45/Serilog.Sinks.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Serilog.Sinks.Console.dll": {"related": ".xml"}}}, "Serilog.Sinks.File/5.0.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net45/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}}, "SharpZipLib/1.4.2": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "SixLabors.Fonts/1.0.0": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.7.0"}, "compile": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}}, "SixLabors.ImageSharp/2.1.4": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0"}, "compile": {"lib/netstandard2.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Configuration.ConfigurationManager/6.0.0": {"type": "package", "dependencies": {"System.Security.Permissions": "6.0.0"}, "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net461/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net461/System.Configuration.ConfigurationManager.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.1.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Security.AccessControl/6.0.0": {"type": "package", "dependencies": {"System.Security.Principal.Windows": "5.0.0"}, "compile": {"lib/net461/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net461/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Xml/6.0.1": {"type": "package", "dependencies": {"System.Security.AccessControl": "6.0.0"}, "frameworkAssemblies": ["System.Security"], "compile": {"lib/net461/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net461/System.Security.Cryptography.Xml.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Permissions/6.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "6.0.0"}, "frameworkAssemblies": ["System.Configuration", "System.Data.OracleClient", "System.Net", "System.Security", "System.ServiceProcess", "System.Transactions", "WindowsBase"], "compile": {"lib/net461/System.Security.Permissions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Security.Permissions.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net461/System.Security.Permissions.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net461/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net461/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net461/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "Alsi.App/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Microsoft.AspNet.WebApi.OwinSelfHost": "5.3.0", "Newtonsoft.Json": "13.0.3", "Serilog.Sinks.Console": "4.1.0", "Serilog.Sinks.File": "5.0.0", "SharpZipLib": "1.4.2", "System.Runtime.CompilerServices.Unsafe": "6.1.1"}, "compile": {"bin/placeholder/Alsi.App.dll": {}}, "runtime": {"bin/placeholder/Alsi.App.dll": {}}}, "Alsi.App.Desktop/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Alsi.App": "1.0.0", "Alsi.Common.Utils": "1.0.0", "MahApps.Metro.IconPacks.Material": "4.11.0", "Microsoft.AspNet.WebApi.Owin": "5.3.0", "Newtonsoft.Json": "13.0.3", "Ookii.Dialogs.Wpf": "5.0.1", "System.Runtime.CompilerServices.Unsafe": "6.1.1"}, "compile": {"bin/placeholder/Alsi.App.Desktop.dll": {}}, "runtime": {"bin/placeholder/Alsi.App.Desktop.dll": {}}}, "Alsi.Common.Log/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Alsi.Common.Utils": "1.0.0", "Serilog.Sinks.File": "5.0.0"}, "compile": {"bin/placeholder/Alsi.Common.Log.dll": {}}, "runtime": {"bin/placeholder/Alsi.Common.Log.dll": {}}}, "Alsi.Common.Parsers/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Alsi.Common.Utils": "1.0.0", "NPOI": "2.6.2", "SharpZipLib": "1.4.2", "System.Runtime.CompilerServices.Unsafe": "6.1.1", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"bin/placeholder/Alsi.Common.Parsers.dll": {}}, "runtime": {"bin/placeholder/Alsi.Common.Parsers.dll": {}}}, "Alsi.Common.Utils/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Newtonsoft.Json": "13.0.3"}, "compile": {"bin/placeholder/Alsi.Common.Utils.dll": {}}, "runtime": {"bin/placeholder/Alsi.Common.Utils.dll": {}}}, "Alsi.Tab.Kit.Core/1.0.0": {"type": "project", "dependencies": {"Alsi.App.Desktop": "1.0.0", "Alsi.Common.Log": "1.0.0", "Alsi.Common.Parsers": "1.0.0", "Alsi.Common.Utils": "1.0.0"}, "compile": {"bin/placeholder/Alsi.Tab.Kit.Core.dll": {}}, "runtime": {"bin/placeholder/Alsi.Tab.Kit.Core.dll": {}}}}, ".NETFramework,Version=v4.6.2/win": {"AutoMapper/8.1.1": {"type": "package", "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net461/AutoMapper.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net461/AutoMapper.dll": {"related": ".pdb;.xml"}}}, "BouncyCastle.Cryptography/2.2.1": {"type": "package", "compile": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}, "runtime": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}}, "Enums.NET/4.0.1": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}}, "MahApps.Metro.IconPacks.Material/4.11.0": {"type": "package", "compile": {"lib/net46/MahApps.Metro.IconPacks.Core.dll": {"related": ".pdb;.xml"}, "lib/net46/MahApps.Metro.IconPacks.Material.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net46/MahApps.Metro.IconPacks.Core.dll": {"related": ".pdb;.xml"}, "lib/net46/MahApps.Metro.IconPacks.Material.dll": {"related": ".pdb;.xml"}}}, "MathNet.Numerics.Signed/4.15.0": {"type": "package", "frameworkAssemblies": ["System", "System.Core", "System.Numerics", "System.Runtime.Serialization"], "compile": {"lib/net461/MathNet.Numerics.dll": {"related": ".xml"}}, "runtime": {"lib/net461/MathNet.Numerics.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Client/6.0.0": {"type": "package", "dependencies": {"Newtonsoft.Json": "13.0.1", "Newtonsoft.Json.Bson": "1.0.2", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["System.Net.Http"], "compile": {"lib/net45/System.Net.Http.Formatting.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Net.Http.Formatting.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Core/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Client": "6.0.0"}, "compile": {"lib/net45/System.Web.Http.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Owin/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Core": "5.3.0", "Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/System.Web.Http.Owin.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.Owin.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.OwinSelfHost/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Owin": "5.3.0", "Microsoft.Owin.Host.HttpListener": "4.2.2", "Microsoft.Owin.Hosting": "4.2.2"}}, "Microsoft.IO.RecyclableMemoryStream/2.3.2": {"type": "package", "dependencies": {"System.Memory": "4.5.5"}, "compile": {"lib/net462/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "Microsoft.Owin/4.2.2": {"type": "package", "dependencies": {"Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.dll": {"related": ".xml"}}}, "Microsoft.Owin.Host.HttpListener/4.2.2": {"type": "package", "dependencies": {"Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.Host.HttpListener.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.Host.HttpListener.dll": {"related": ".xml"}}}, "Microsoft.Owin.Hosting/4.2.2": {"type": "package", "dependencies": {"Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.Hosting.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "dependencies": {"Newtonsoft.Json": "12.0.1"}, "compile": {"lib/net45/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}}, "NPOI/2.6.2": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.2.1", "Enums.NET": "4.0.1", "MathNet.Numerics.Signed": "4.15.0", "Microsoft.IO.RecyclableMemoryStream": "2.3.2", "SharpZipLib": "1.3.3", "SixLabors.Fonts": "1.0.0", "SixLabors.ImageSharp": "2.1.4", "System.Configuration.ConfigurationManager": "6.0.0", "System.Security.Cryptography.Xml": "6.0.1"}, "compile": {"lib/netstandard2.0/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}}, "Ookii.Dialogs.Wpf/5.0.1": {"type": "package", "frameworkAssemblies": ["PresentationCore", "PresentationFramework", "System", "System.Core", "System.Design", "System.Drawing", "System.Security", "WindowsBase"], "compile": {"lib/net462/Ookii.Dialogs.Wpf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/Ookii.Dialogs.Wpf.dll": {"related": ".pdb;.xml"}}}, "Owin/1.0.0": {"type": "package", "compile": {"lib/net40/Owin.dll": {}}, "runtime": {"lib/net40/Owin.dll": {}}}, "Serilog/2.10.0": {"type": "package", "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Core"], "compile": {"lib/net46/Serilog.dll": {"related": ".xml"}}, "runtime": {"lib/net46/Serilog.dll": {"related": ".xml"}}}, "Serilog.Sinks.Console/4.1.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net45/Serilog.Sinks.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Serilog.Sinks.Console.dll": {"related": ".xml"}}}, "Serilog.Sinks.File/5.0.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net45/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}}, "SharpZipLib/1.4.2": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "SixLabors.Fonts/1.0.0": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.7.0"}, "compile": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}}, "SixLabors.ImageSharp/2.1.4": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0"}, "compile": {"lib/netstandard2.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Configuration.ConfigurationManager/6.0.0": {"type": "package", "dependencies": {"System.Security.Permissions": "6.0.0"}, "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net461/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net461/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.1.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Security.AccessControl/6.0.0": {"type": "package", "dependencies": {"System.Security.Principal.Windows": "5.0.0"}, "compile": {"lib/net461/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net461/System.Security.AccessControl.dll": {"related": ".xml"}}}, "System.Security.Cryptography.Xml/6.0.1": {"type": "package", "dependencies": {"System.Security.AccessControl": "6.0.0"}, "frameworkAssemblies": ["System.Security"], "compile": {"lib/net461/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net461/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}}, "System.Security.Permissions/6.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "6.0.0"}, "frameworkAssemblies": ["System.Configuration", "System.Data.OracleClient", "System.Net", "System.Security", "System.ServiceProcess", "System.Transactions", "WindowsBase"], "compile": {"lib/net461/System.Security.Permissions.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net461/System.Security.Permissions.dll": {"related": ".xml"}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net461/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net461/System.Security.Principal.Windows.dll": {"related": ".xml"}}}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "Alsi.App/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Microsoft.AspNet.WebApi.OwinSelfHost": "5.3.0", "Newtonsoft.Json": "13.0.3", "Serilog.Sinks.Console": "4.1.0", "Serilog.Sinks.File": "5.0.0", "SharpZipLib": "1.4.2", "System.Runtime.CompilerServices.Unsafe": "6.1.1"}, "compile": {"bin/placeholder/Alsi.App.dll": {}}, "runtime": {"bin/placeholder/Alsi.App.dll": {}}}, "Alsi.App.Desktop/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Alsi.App": "1.0.0", "Alsi.Common.Utils": "1.0.0", "MahApps.Metro.IconPacks.Material": "4.11.0", "Microsoft.AspNet.WebApi.Owin": "5.3.0", "Newtonsoft.Json": "13.0.3", "Ookii.Dialogs.Wpf": "5.0.1", "System.Runtime.CompilerServices.Unsafe": "6.1.1"}, "compile": {"bin/placeholder/Alsi.App.Desktop.dll": {}}, "runtime": {"bin/placeholder/Alsi.App.Desktop.dll": {}}}, "Alsi.Common.Log/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Alsi.Common.Utils": "1.0.0", "Serilog.Sinks.File": "5.0.0"}, "compile": {"bin/placeholder/Alsi.Common.Log.dll": {}}, "runtime": {"bin/placeholder/Alsi.Common.Log.dll": {}}}, "Alsi.Common.Parsers/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Alsi.Common.Utils": "1.0.0", "NPOI": "2.6.2", "SharpZipLib": "1.4.2", "System.Runtime.CompilerServices.Unsafe": "6.1.1", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"bin/placeholder/Alsi.Common.Parsers.dll": {}}, "runtime": {"bin/placeholder/Alsi.Common.Parsers.dll": {}}}, "Alsi.Common.Utils/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Newtonsoft.Json": "13.0.3"}, "compile": {"bin/placeholder/Alsi.Common.Utils.dll": {}}, "runtime": {"bin/placeholder/Alsi.Common.Utils.dll": {}}}, "Alsi.Tab.Kit.Core/1.0.0": {"type": "project", "dependencies": {"Alsi.App.Desktop": "1.0.0", "Alsi.Common.Log": "1.0.0", "Alsi.Common.Parsers": "1.0.0", "Alsi.Common.Utils": "1.0.0"}, "compile": {"bin/placeholder/Alsi.Tab.Kit.Core.dll": {}}, "runtime": {"bin/placeholder/Alsi.Tab.Kit.Core.dll": {}}}}, ".NETFramework,Version=v4.6.2/win-arm64": {"AutoMapper/8.1.1": {"type": "package", "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net461/AutoMapper.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net461/AutoMapper.dll": {"related": ".pdb;.xml"}}}, "BouncyCastle.Cryptography/2.2.1": {"type": "package", "compile": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}, "runtime": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}}, "Enums.NET/4.0.1": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}}, "MahApps.Metro.IconPacks.Material/4.11.0": {"type": "package", "compile": {"lib/net46/MahApps.Metro.IconPacks.Core.dll": {"related": ".pdb;.xml"}, "lib/net46/MahApps.Metro.IconPacks.Material.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net46/MahApps.Metro.IconPacks.Core.dll": {"related": ".pdb;.xml"}, "lib/net46/MahApps.Metro.IconPacks.Material.dll": {"related": ".pdb;.xml"}}}, "MathNet.Numerics.Signed/4.15.0": {"type": "package", "frameworkAssemblies": ["System", "System.Core", "System.Numerics", "System.Runtime.Serialization"], "compile": {"lib/net461/MathNet.Numerics.dll": {"related": ".xml"}}, "runtime": {"lib/net461/MathNet.Numerics.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Client/6.0.0": {"type": "package", "dependencies": {"Newtonsoft.Json": "13.0.1", "Newtonsoft.Json.Bson": "1.0.2", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["System.Net.Http"], "compile": {"lib/net45/System.Net.Http.Formatting.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Net.Http.Formatting.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Core/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Client": "6.0.0"}, "compile": {"lib/net45/System.Web.Http.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Owin/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Core": "5.3.0", "Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/System.Web.Http.Owin.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.Owin.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.OwinSelfHost/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Owin": "5.3.0", "Microsoft.Owin.Host.HttpListener": "4.2.2", "Microsoft.Owin.Hosting": "4.2.2"}}, "Microsoft.IO.RecyclableMemoryStream/2.3.2": {"type": "package", "dependencies": {"System.Memory": "4.5.5"}, "compile": {"lib/net462/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "Microsoft.Owin/4.2.2": {"type": "package", "dependencies": {"Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.dll": {"related": ".xml"}}}, "Microsoft.Owin.Host.HttpListener/4.2.2": {"type": "package", "dependencies": {"Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.Host.HttpListener.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.Host.HttpListener.dll": {"related": ".xml"}}}, "Microsoft.Owin.Hosting/4.2.2": {"type": "package", "dependencies": {"Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.Hosting.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "dependencies": {"Newtonsoft.Json": "12.0.1"}, "compile": {"lib/net45/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}}, "NPOI/2.6.2": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.2.1", "Enums.NET": "4.0.1", "MathNet.Numerics.Signed": "4.15.0", "Microsoft.IO.RecyclableMemoryStream": "2.3.2", "SharpZipLib": "1.3.3", "SixLabors.Fonts": "1.0.0", "SixLabors.ImageSharp": "2.1.4", "System.Configuration.ConfigurationManager": "6.0.0", "System.Security.Cryptography.Xml": "6.0.1"}, "compile": {"lib/netstandard2.0/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}}, "Ookii.Dialogs.Wpf/5.0.1": {"type": "package", "frameworkAssemblies": ["PresentationCore", "PresentationFramework", "System", "System.Core", "System.Design", "System.Drawing", "System.Security", "WindowsBase"], "compile": {"lib/net462/Ookii.Dialogs.Wpf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/Ookii.Dialogs.Wpf.dll": {"related": ".pdb;.xml"}}}, "Owin/1.0.0": {"type": "package", "compile": {"lib/net40/Owin.dll": {}}, "runtime": {"lib/net40/Owin.dll": {}}}, "Serilog/2.10.0": {"type": "package", "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Core"], "compile": {"lib/net46/Serilog.dll": {"related": ".xml"}}, "runtime": {"lib/net46/Serilog.dll": {"related": ".xml"}}}, "Serilog.Sinks.Console/4.1.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net45/Serilog.Sinks.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Serilog.Sinks.Console.dll": {"related": ".xml"}}}, "Serilog.Sinks.File/5.0.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net45/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}}, "SharpZipLib/1.4.2": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "SixLabors.Fonts/1.0.0": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.7.0"}, "compile": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}}, "SixLabors.ImageSharp/2.1.4": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0"}, "compile": {"lib/netstandard2.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Configuration.ConfigurationManager/6.0.0": {"type": "package", "dependencies": {"System.Security.Permissions": "6.0.0"}, "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net461/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.1.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Security.AccessControl/6.0.0": {"type": "package", "dependencies": {"System.Security.Principal.Windows": "5.0.0"}, "compile": {"lib/net461/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Security.AccessControl.dll": {"related": ".xml"}}}, "System.Security.Cryptography.Xml/6.0.1": {"type": "package", "dependencies": {"System.Security.AccessControl": "6.0.0"}, "frameworkAssemblies": ["System.Security"], "compile": {"lib/net461/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}}, "System.Security.Permissions/6.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "6.0.0"}, "frameworkAssemblies": ["System.Configuration", "System.Data.OracleClient", "System.Net", "System.Security", "System.ServiceProcess", "System.Transactions", "WindowsBase"], "compile": {"lib/net461/System.Security.Permissions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Security.Permissions.dll": {"related": ".xml"}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net461/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Security.Principal.Windows.dll": {"related": ".xml"}}}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "Alsi.App/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Microsoft.AspNet.WebApi.OwinSelfHost": "5.3.0", "Newtonsoft.Json": "13.0.3", "Serilog.Sinks.Console": "4.1.0", "Serilog.Sinks.File": "5.0.0", "SharpZipLib": "1.4.2", "System.Runtime.CompilerServices.Unsafe": "6.1.1"}, "compile": {"bin/placeholder/Alsi.App.dll": {}}, "runtime": {"bin/placeholder/Alsi.App.dll": {}}}, "Alsi.App.Desktop/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Alsi.App": "1.0.0", "Alsi.Common.Utils": "1.0.0", "MahApps.Metro.IconPacks.Material": "4.11.0", "Microsoft.AspNet.WebApi.Owin": "5.3.0", "Newtonsoft.Json": "13.0.3", "Ookii.Dialogs.Wpf": "5.0.1", "System.Runtime.CompilerServices.Unsafe": "6.1.1"}, "compile": {"bin/placeholder/Alsi.App.Desktop.dll": {}}, "runtime": {"bin/placeholder/Alsi.App.Desktop.dll": {}}}, "Alsi.Common.Log/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Alsi.Common.Utils": "1.0.0", "Serilog.Sinks.File": "5.0.0"}, "compile": {"bin/placeholder/Alsi.Common.Log.dll": {}}, "runtime": {"bin/placeholder/Alsi.Common.Log.dll": {}}}, "Alsi.Common.Parsers/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Alsi.Common.Utils": "1.0.0", "NPOI": "2.6.2", "SharpZipLib": "1.4.2", "System.Runtime.CompilerServices.Unsafe": "6.1.1", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"bin/placeholder/Alsi.Common.Parsers.dll": {}}, "runtime": {"bin/placeholder/Alsi.Common.Parsers.dll": {}}}, "Alsi.Common.Utils/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Newtonsoft.Json": "13.0.3"}, "compile": {"bin/placeholder/Alsi.Common.Utils.dll": {}}, "runtime": {"bin/placeholder/Alsi.Common.Utils.dll": {}}}, "Alsi.Tab.Kit.Core/1.0.0": {"type": "project", "dependencies": {"Alsi.App.Desktop": "1.0.0", "Alsi.Common.Log": "1.0.0", "Alsi.Common.Parsers": "1.0.0", "Alsi.Common.Utils": "1.0.0"}, "compile": {"bin/placeholder/Alsi.Tab.Kit.Core.dll": {}}, "runtime": {"bin/placeholder/Alsi.Tab.Kit.Core.dll": {}}}}, ".NETFramework,Version=v4.6.2/win-x64": {"AutoMapper/8.1.1": {"type": "package", "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net461/AutoMapper.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net461/AutoMapper.dll": {"related": ".pdb;.xml"}}}, "BouncyCastle.Cryptography/2.2.1": {"type": "package", "compile": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}, "runtime": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}}, "Enums.NET/4.0.1": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}}, "MahApps.Metro.IconPacks.Material/4.11.0": {"type": "package", "compile": {"lib/net46/MahApps.Metro.IconPacks.Core.dll": {"related": ".pdb;.xml"}, "lib/net46/MahApps.Metro.IconPacks.Material.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net46/MahApps.Metro.IconPacks.Core.dll": {"related": ".pdb;.xml"}, "lib/net46/MahApps.Metro.IconPacks.Material.dll": {"related": ".pdb;.xml"}}}, "MathNet.Numerics.Signed/4.15.0": {"type": "package", "frameworkAssemblies": ["System", "System.Core", "System.Numerics", "System.Runtime.Serialization"], "compile": {"lib/net461/MathNet.Numerics.dll": {"related": ".xml"}}, "runtime": {"lib/net461/MathNet.Numerics.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Client/6.0.0": {"type": "package", "dependencies": {"Newtonsoft.Json": "13.0.1", "Newtonsoft.Json.Bson": "1.0.2", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["System.Net.Http"], "compile": {"lib/net45/System.Net.Http.Formatting.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Net.Http.Formatting.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Core/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Client": "6.0.0"}, "compile": {"lib/net45/System.Web.Http.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Owin/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Core": "5.3.0", "Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/System.Web.Http.Owin.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.Owin.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.OwinSelfHost/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Owin": "5.3.0", "Microsoft.Owin.Host.HttpListener": "4.2.2", "Microsoft.Owin.Hosting": "4.2.2"}}, "Microsoft.IO.RecyclableMemoryStream/2.3.2": {"type": "package", "dependencies": {"System.Memory": "4.5.5"}, "compile": {"lib/net462/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "Microsoft.Owin/4.2.2": {"type": "package", "dependencies": {"Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.dll": {"related": ".xml"}}}, "Microsoft.Owin.Host.HttpListener/4.2.2": {"type": "package", "dependencies": {"Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.Host.HttpListener.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.Host.HttpListener.dll": {"related": ".xml"}}}, "Microsoft.Owin.Hosting/4.2.2": {"type": "package", "dependencies": {"Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.Hosting.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "dependencies": {"Newtonsoft.Json": "12.0.1"}, "compile": {"lib/net45/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}}, "NPOI/2.6.2": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.2.1", "Enums.NET": "4.0.1", "MathNet.Numerics.Signed": "4.15.0", "Microsoft.IO.RecyclableMemoryStream": "2.3.2", "SharpZipLib": "1.3.3", "SixLabors.Fonts": "1.0.0", "SixLabors.ImageSharp": "2.1.4", "System.Configuration.ConfigurationManager": "6.0.0", "System.Security.Cryptography.Xml": "6.0.1"}, "compile": {"lib/netstandard2.0/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}}, "Ookii.Dialogs.Wpf/5.0.1": {"type": "package", "frameworkAssemblies": ["PresentationCore", "PresentationFramework", "System", "System.Core", "System.Design", "System.Drawing", "System.Security", "WindowsBase"], "compile": {"lib/net462/Ookii.Dialogs.Wpf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/Ookii.Dialogs.Wpf.dll": {"related": ".pdb;.xml"}}}, "Owin/1.0.0": {"type": "package", "compile": {"lib/net40/Owin.dll": {}}, "runtime": {"lib/net40/Owin.dll": {}}}, "Serilog/2.10.0": {"type": "package", "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Core"], "compile": {"lib/net46/Serilog.dll": {"related": ".xml"}}, "runtime": {"lib/net46/Serilog.dll": {"related": ".xml"}}}, "Serilog.Sinks.Console/4.1.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net45/Serilog.Sinks.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Serilog.Sinks.Console.dll": {"related": ".xml"}}}, "Serilog.Sinks.File/5.0.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net45/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}}, "SharpZipLib/1.4.2": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "SixLabors.Fonts/1.0.0": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.7.0"}, "compile": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}}, "SixLabors.ImageSharp/2.1.4": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0"}, "compile": {"lib/netstandard2.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Configuration.ConfigurationManager/6.0.0": {"type": "package", "dependencies": {"System.Security.Permissions": "6.0.0"}, "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net461/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.1.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Security.AccessControl/6.0.0": {"type": "package", "dependencies": {"System.Security.Principal.Windows": "5.0.0"}, "compile": {"lib/net461/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Security.AccessControl.dll": {"related": ".xml"}}}, "System.Security.Cryptography.Xml/6.0.1": {"type": "package", "dependencies": {"System.Security.AccessControl": "6.0.0"}, "frameworkAssemblies": ["System.Security"], "compile": {"lib/net461/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}}, "System.Security.Permissions/6.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "6.0.0"}, "frameworkAssemblies": ["System.Configuration", "System.Data.OracleClient", "System.Net", "System.Security", "System.ServiceProcess", "System.Transactions", "WindowsBase"], "compile": {"lib/net461/System.Security.Permissions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Security.Permissions.dll": {"related": ".xml"}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net461/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Security.Principal.Windows.dll": {"related": ".xml"}}}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "Alsi.App/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Microsoft.AspNet.WebApi.OwinSelfHost": "5.3.0", "Newtonsoft.Json": "13.0.3", "Serilog.Sinks.Console": "4.1.0", "Serilog.Sinks.File": "5.0.0", "SharpZipLib": "1.4.2", "System.Runtime.CompilerServices.Unsafe": "6.1.1"}, "compile": {"bin/placeholder/Alsi.App.dll": {}}, "runtime": {"bin/placeholder/Alsi.App.dll": {}}}, "Alsi.App.Desktop/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Alsi.App": "1.0.0", "Alsi.Common.Utils": "1.0.0", "MahApps.Metro.IconPacks.Material": "4.11.0", "Microsoft.AspNet.WebApi.Owin": "5.3.0", "Newtonsoft.Json": "13.0.3", "Ookii.Dialogs.Wpf": "5.0.1", "System.Runtime.CompilerServices.Unsafe": "6.1.1"}, "compile": {"bin/placeholder/Alsi.App.Desktop.dll": {}}, "runtime": {"bin/placeholder/Alsi.App.Desktop.dll": {}}}, "Alsi.Common.Log/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Alsi.Common.Utils": "1.0.0", "Serilog.Sinks.File": "5.0.0"}, "compile": {"bin/placeholder/Alsi.Common.Log.dll": {}}, "runtime": {"bin/placeholder/Alsi.Common.Log.dll": {}}}, "Alsi.Common.Parsers/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Alsi.Common.Utils": "1.0.0", "NPOI": "2.6.2", "SharpZipLib": "1.4.2", "System.Runtime.CompilerServices.Unsafe": "6.1.1", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"bin/placeholder/Alsi.Common.Parsers.dll": {}}, "runtime": {"bin/placeholder/Alsi.Common.Parsers.dll": {}}}, "Alsi.Common.Utils/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Newtonsoft.Json": "13.0.3"}, "compile": {"bin/placeholder/Alsi.Common.Utils.dll": {}}, "runtime": {"bin/placeholder/Alsi.Common.Utils.dll": {}}}, "Alsi.Tab.Kit.Core/1.0.0": {"type": "project", "dependencies": {"Alsi.App.Desktop": "1.0.0", "Alsi.Common.Log": "1.0.0", "Alsi.Common.Parsers": "1.0.0", "Alsi.Common.Utils": "1.0.0"}, "compile": {"bin/placeholder/Alsi.Tab.Kit.Core.dll": {}}, "runtime": {"bin/placeholder/Alsi.Tab.Kit.Core.dll": {}}}}, ".NETFramework,Version=v4.6.2/win-x86": {"AutoMapper/8.1.1": {"type": "package", "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net461/AutoMapper.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net461/AutoMapper.dll": {"related": ".pdb;.xml"}}}, "BouncyCastle.Cryptography/2.2.1": {"type": "package", "compile": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}, "runtime": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}}, "Enums.NET/4.0.1": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}}, "MahApps.Metro.IconPacks.Material/4.11.0": {"type": "package", "compile": {"lib/net46/MahApps.Metro.IconPacks.Core.dll": {"related": ".pdb;.xml"}, "lib/net46/MahApps.Metro.IconPacks.Material.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net46/MahApps.Metro.IconPacks.Core.dll": {"related": ".pdb;.xml"}, "lib/net46/MahApps.Metro.IconPacks.Material.dll": {"related": ".pdb;.xml"}}}, "MathNet.Numerics.Signed/4.15.0": {"type": "package", "frameworkAssemblies": ["System", "System.Core", "System.Numerics", "System.Runtime.Serialization"], "compile": {"lib/net461/MathNet.Numerics.dll": {"related": ".xml"}}, "runtime": {"lib/net461/MathNet.Numerics.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Client/6.0.0": {"type": "package", "dependencies": {"Newtonsoft.Json": "13.0.1", "Newtonsoft.Json.Bson": "1.0.2", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["System.Net.Http"], "compile": {"lib/net45/System.Net.Http.Formatting.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Net.Http.Formatting.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Core/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Client": "6.0.0"}, "compile": {"lib/net45/System.Web.Http.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Owin/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Core": "5.3.0", "Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/System.Web.Http.Owin.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.Owin.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.OwinSelfHost/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Owin": "5.3.0", "Microsoft.Owin.Host.HttpListener": "4.2.2", "Microsoft.Owin.Hosting": "4.2.2"}}, "Microsoft.IO.RecyclableMemoryStream/2.3.2": {"type": "package", "dependencies": {"System.Memory": "4.5.5"}, "compile": {"lib/net462/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "Microsoft.Owin/4.2.2": {"type": "package", "dependencies": {"Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.dll": {"related": ".xml"}}}, "Microsoft.Owin.Host.HttpListener/4.2.2": {"type": "package", "dependencies": {"Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.Host.HttpListener.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.Host.HttpListener.dll": {"related": ".xml"}}}, "Microsoft.Owin.Hosting/4.2.2": {"type": "package", "dependencies": {"Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.Hosting.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "dependencies": {"Newtonsoft.Json": "12.0.1"}, "compile": {"lib/net45/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}}, "NPOI/2.6.2": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.2.1", "Enums.NET": "4.0.1", "MathNet.Numerics.Signed": "4.15.0", "Microsoft.IO.RecyclableMemoryStream": "2.3.2", "SharpZipLib": "1.3.3", "SixLabors.Fonts": "1.0.0", "SixLabors.ImageSharp": "2.1.4", "System.Configuration.ConfigurationManager": "6.0.0", "System.Security.Cryptography.Xml": "6.0.1"}, "compile": {"lib/netstandard2.0/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}}, "Ookii.Dialogs.Wpf/5.0.1": {"type": "package", "frameworkAssemblies": ["PresentationCore", "PresentationFramework", "System", "System.Core", "System.Design", "System.Drawing", "System.Security", "WindowsBase"], "compile": {"lib/net462/Ookii.Dialogs.Wpf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/Ookii.Dialogs.Wpf.dll": {"related": ".pdb;.xml"}}}, "Owin/1.0.0": {"type": "package", "compile": {"lib/net40/Owin.dll": {}}, "runtime": {"lib/net40/Owin.dll": {}}}, "Serilog/2.10.0": {"type": "package", "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Core"], "compile": {"lib/net46/Serilog.dll": {"related": ".xml"}}, "runtime": {"lib/net46/Serilog.dll": {"related": ".xml"}}}, "Serilog.Sinks.Console/4.1.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net45/Serilog.Sinks.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Serilog.Sinks.Console.dll": {"related": ".xml"}}}, "Serilog.Sinks.File/5.0.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net45/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}}, "SharpZipLib/1.4.2": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "SixLabors.Fonts/1.0.0": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.7.0"}, "compile": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}}, "SixLabors.ImageSharp/2.1.4": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0"}, "compile": {"lib/netstandard2.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Configuration.ConfigurationManager/6.0.0": {"type": "package", "dependencies": {"System.Security.Permissions": "6.0.0"}, "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net461/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.1.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Security.AccessControl/6.0.0": {"type": "package", "dependencies": {"System.Security.Principal.Windows": "5.0.0"}, "compile": {"lib/net461/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Security.AccessControl.dll": {"related": ".xml"}}}, "System.Security.Cryptography.Xml/6.0.1": {"type": "package", "dependencies": {"System.Security.AccessControl": "6.0.0"}, "frameworkAssemblies": ["System.Security"], "compile": {"lib/net461/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}}, "System.Security.Permissions/6.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "6.0.0"}, "frameworkAssemblies": ["System.Configuration", "System.Data.OracleClient", "System.Net", "System.Security", "System.ServiceProcess", "System.Transactions", "WindowsBase"], "compile": {"lib/net461/System.Security.Permissions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Security.Permissions.dll": {"related": ".xml"}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net461/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Security.Principal.Windows.dll": {"related": ".xml"}}}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "Alsi.App/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Microsoft.AspNet.WebApi.OwinSelfHost": "5.3.0", "Newtonsoft.Json": "13.0.3", "Serilog.Sinks.Console": "4.1.0", "Serilog.Sinks.File": "5.0.0", "SharpZipLib": "1.4.2", "System.Runtime.CompilerServices.Unsafe": "6.1.1"}, "compile": {"bin/placeholder/Alsi.App.dll": {}}, "runtime": {"bin/placeholder/Alsi.App.dll": {}}}, "Alsi.App.Desktop/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Alsi.App": "1.0.0", "Alsi.Common.Utils": "1.0.0", "MahApps.Metro.IconPacks.Material": "4.11.0", "Microsoft.AspNet.WebApi.Owin": "5.3.0", "Newtonsoft.Json": "13.0.3", "Ookii.Dialogs.Wpf": "5.0.1", "System.Runtime.CompilerServices.Unsafe": "6.1.1"}, "compile": {"bin/placeholder/Alsi.App.Desktop.dll": {}}, "runtime": {"bin/placeholder/Alsi.App.Desktop.dll": {}}}, "Alsi.Common.Log/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Alsi.Common.Utils": "1.0.0", "Serilog.Sinks.File": "5.0.0"}, "compile": {"bin/placeholder/Alsi.Common.Log.dll": {}}, "runtime": {"bin/placeholder/Alsi.Common.Log.dll": {}}}, "Alsi.Common.Parsers/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Alsi.Common.Utils": "1.0.0", "NPOI": "2.6.2", "SharpZipLib": "1.4.2", "System.Runtime.CompilerServices.Unsafe": "6.1.1", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"bin/placeholder/Alsi.Common.Parsers.dll": {}}, "runtime": {"bin/placeholder/Alsi.Common.Parsers.dll": {}}}, "Alsi.Common.Utils/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Newtonsoft.Json": "13.0.3"}, "compile": {"bin/placeholder/Alsi.Common.Utils.dll": {}}, "runtime": {"bin/placeholder/Alsi.Common.Utils.dll": {}}}, "Alsi.Tab.Kit.Core/1.0.0": {"type": "project", "dependencies": {"Alsi.App.Desktop": "1.0.0", "Alsi.Common.Log": "1.0.0", "Alsi.Common.Parsers": "1.0.0", "Alsi.Common.Utils": "1.0.0"}, "compile": {"bin/placeholder/Alsi.Tab.Kit.Core.dll": {}}, "runtime": {"bin/placeholder/Alsi.Tab.Kit.Core.dll": {}}}}}, "libraries": {"AutoMapper/8.1.1": {"sha512": "GKBliBukkLG0z/U0T4Jy+yEpr5bKDImQ9nEy/T8jqNulwNkWDjiEWa9TL4yCxIhleDbmVmw2xYSaJ3Eb2OcSUg==", "type": "package", "path": "automapper/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "automapper.8.1.1.nupkg.sha512", "automapper.nuspec", "lib/net461/AutoMapper.dll", "lib/net461/AutoMapper.pdb", "lib/net461/AutoMapper.xml", "lib/netstandard2.0/AutoMapper.dll", "lib/netstandard2.0/AutoMapper.pdb", "lib/netstandard2.0/AutoMapper.xml"]}, "BouncyCastle.Cryptography/2.2.1": {"sha512": "A6Zr52zVqJKt18ZBsTnX0qhG0kwIQftVAjLmszmkiR/trSp8H+xj1gUOzk7XHwaKgyREMSV1v9XaKrBUeIOdvQ==", "type": "package", "path": "bouncycastle.cryptography/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "bouncycastle.cryptography.2.2.1.nupkg.sha512", "bouncycastle.cryptography.nuspec", "lib/net461/BouncyCastle.Cryptography.dll", "lib/net461/BouncyCastle.Cryptography.xml", "lib/net6.0/BouncyCastle.Cryptography.dll", "lib/net6.0/BouncyCastle.Cryptography.xml", "lib/netstandard2.0/BouncyCastle.Cryptography.dll", "lib/netstandard2.0/BouncyCastle.Cryptography.xml", "packageIcon.png"]}, "Enums.NET/4.0.1": {"sha512": "OUGCd5L8zHZ61GAf436G0gf/H6yrSUkEpV5vm2CbCUuz9Rx7iLFLP5iHSSfmOtqNpuyo4vYte0CvYEmPZXRmRQ==", "type": "package", "path": "enums.net/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "enums.net.4.0.1.nupkg.sha512", "enums.net.nuspec", "lib/net45/Enums.NET.dll", "lib/net45/Enums.NET.pdb", "lib/net45/Enums.NET.xml", "lib/netcoreapp3.0/Enums.NET.dll", "lib/netcoreapp3.0/Enums.NET.pdb", "lib/netcoreapp3.0/Enums.NET.xml", "lib/netstandard1.0/Enums.NET.dll", "lib/netstandard1.0/Enums.NET.pdb", "lib/netstandard1.0/Enums.NET.xml", "lib/netstandard1.1/Enums.NET.dll", "lib/netstandard1.1/Enums.NET.pdb", "lib/netstandard1.1/Enums.NET.xml", "lib/netstandard1.3/Enums.NET.dll", "lib/netstandard1.3/Enums.NET.pdb", "lib/netstandard1.3/Enums.NET.xml", "lib/netstandard2.0/Enums.NET.dll", "lib/netstandard2.0/Enums.NET.pdb", "lib/netstandard2.0/Enums.NET.xml", "lib/netstandard2.1/Enums.NET.dll", "lib/netstandard2.1/Enums.NET.pdb", "lib/netstandard2.1/Enums.NET.xml"]}, "MahApps.Metro.IconPacks.Material/4.11.0": {"sha512": "ZCuuxWHvXxl39XBYXk4PCMUVtw1pevT5AE1qsj7VRy2tRtDYbzHWYW0FbnH+Vf0rP7BogZrRlKns3zFEkDEjFQ==", "type": "package", "path": "mahapps.metro.iconpacks.material/4.11.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/MahApps.Metro.IconPacks.Core.dll", "lib/net45/MahApps.Metro.IconPacks.Core.pdb", "lib/net45/MahApps.Metro.IconPacks.Core.xml", "lib/net45/MahApps.Metro.IconPacks.Material.dll", "lib/net45/MahApps.Metro.IconPacks.Material.pdb", "lib/net45/MahApps.Metro.IconPacks.Material.xml", "lib/net46/MahApps.Metro.IconPacks.Core.dll", "lib/net46/MahApps.Metro.IconPacks.Core.pdb", "lib/net46/MahApps.Metro.IconPacks.Core.xml", "lib/net46/MahApps.Metro.IconPacks.Material.dll", "lib/net46/MahApps.Metro.IconPacks.Material.pdb", "lib/net46/MahApps.Metro.IconPacks.Material.xml", "lib/net47/MahApps.Metro.IconPacks.Core.dll", "lib/net47/MahApps.Metro.IconPacks.Core.pdb", "lib/net47/MahApps.Metro.IconPacks.Core.xml", "lib/net47/MahApps.Metro.IconPacks.Material.dll", "lib/net47/MahApps.Metro.IconPacks.Material.pdb", "lib/net47/MahApps.Metro.IconPacks.Material.xml", "lib/net5.0-windows7.0/MahApps.Metro.IconPacks.Core.dll", "lib/net5.0-windows7.0/MahApps.Metro.IconPacks.Core.pdb", "lib/net5.0-windows7.0/MahApps.Metro.IconPacks.Core.xml", "lib/net5.0-windows7.0/MahApps.Metro.IconPacks.Material.dll", "lib/net5.0-windows7.0/MahApps.Metro.IconPacks.Material.pdb", "lib/net5.0-windows7.0/MahApps.Metro.IconPacks.Material.xml", "lib/netcoreapp3.0/MahApps.Metro.IconPacks.Core.dll", "lib/netcoreapp3.0/MahApps.Metro.IconPacks.Core.pdb", "lib/netcoreapp3.0/MahApps.Metro.IconPacks.Core.xml", "lib/netcoreapp3.0/MahApps.Metro.IconPacks.Material.dll", "lib/netcoreapp3.0/MahApps.Metro.IconPacks.Material.pdb", "lib/netcoreapp3.0/MahApps.Metro.IconPacks.Material.xml", "lib/netcoreapp3.1/MahApps.Metro.IconPacks.Core.dll", "lib/netcoreapp3.1/MahApps.Metro.IconPacks.Core.pdb", "lib/netcoreapp3.1/MahApps.Metro.IconPacks.Core.xml", "lib/netcoreapp3.1/MahApps.Metro.IconPacks.Material.dll", "lib/netcoreapp3.1/MahApps.Metro.IconPacks.Material.pdb", "lib/netcoreapp3.1/MahApps.Metro.IconPacks.Material.xml", "lib/uap10.0.16299/MahApps.Metro.IconPacks.Core.dll", "lib/uap10.0.16299/MahApps.Metro.IconPacks.Core.pdb", "lib/uap10.0.16299/MahApps.Metro.IconPacks.Core.pri", "lib/uap10.0.16299/MahApps.Metro.IconPacks.Core.xml", "lib/uap10.0.16299/MahApps.Metro.IconPacks.Material.dll", "lib/uap10.0.16299/MahApps.Metro.IconPacks.Material.pdb", "lib/uap10.0.16299/MahApps.Metro.IconPacks.Material.pri", "lib/uap10.0.16299/MahApps.Metro.IconPacks.Material.xml", "lib/uap10.0.16299/MahApps.Metro.IconPacks.Material/MahApps.Metro.IconPacks.Material.xr.xml", "logo_small.png", "mahapps.metro.iconpacks.material.4.11.0.nupkg.sha512", "mahapps.metro.iconpacks.material.nuspec"]}, "MathNet.Numerics.Signed/4.15.0": {"sha512": "LFjukMRatkg9dgRM7U/gM4uKgaWAX7E0lt3fsVDTPdtBIVuh7uPlksDie290br1/tv1a4Ar/Bz9ywCPSL8PhHg==", "type": "package", "path": "mathnet.numerics.signed/4.15.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net40/MathNet.Numerics.dll", "lib/net40/MathNet.Numerics.xml", "lib/net461/MathNet.Numerics.dll", "lib/net461/MathNet.Numerics.xml", "lib/netstandard1.3/MathNet.Numerics.dll", "lib/netstandard1.3/MathNet.Numerics.xml", "lib/netstandard2.0/MathNet.Numerics.dll", "lib/netstandard2.0/MathNet.Numerics.xml", "mathnet.numerics.signed.4.15.0.nupkg.sha512", "mathnet.numerics.signed.nuspec"]}, "Microsoft.AspNet.WebApi.Client/6.0.0": {"sha512": "zXeWP03dTo67AoDHUzR+/urck0KFssdCKOC+dq7Nv1V2YbFh/nIg09L0/3wSvyRONEdwxGB/ssEGmPNIIhAcAw==", "type": "package", "path": "microsoft.aspnet.webapi.client/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "NET.icon.png", "NET_Library_EULA_ENU.txt", "lib/net45/System.Net.Http.Formatting.dll", "lib/net45/System.Net.Http.Formatting.xml", "lib/netstandard1.3/System.Net.Http.Formatting.dll", "lib/netstandard1.3/System.Net.Http.Formatting.xml", "lib/netstandard2.0/System.Net.Http.Formatting.dll", "lib/netstandard2.0/System.Net.Http.Formatting.xml", "microsoft.aspnet.webapi.client.6.0.0.nupkg.sha512", "microsoft.aspnet.webapi.client.nuspec"]}, "Microsoft.AspNet.WebApi.Core/5.3.0": {"sha512": "h0oLsUFPgoB1R+6ichy1bniAs4oC6w6XrPsEgn+LuQBxBGskN0djSOSX7hzL8LTFEZUTdsh/3ShjRu1Mb2QRfw==", "type": "package", "path": "microsoft.aspnet.webapi.core/5.3.0", "files": [".nupkg.metadata", ".signature.p7s", "Content/web.config.transform", "NET.icon.png", "NET_Library_EULA_ENU.txt", "lib/net45/System.Web.Http.dll", "lib/net45/System.Web.Http.xml", "microsoft.aspnet.webapi.core.5.3.0.nupkg.sha512", "microsoft.aspnet.webapi.core.nuspec"]}, "Microsoft.AspNet.WebApi.Owin/5.3.0": {"sha512": "goLNSAd5Vzp6nKlCueU9IeV4HbqCYYhl2qjAPNJcvyoK1W3uq3AQeebRuFZeQ5zJG9+ACG5jCpOhEu98gw79hg==", "type": "package", "path": "microsoft.aspnet.webapi.owin/5.3.0", "files": [".nupkg.metadata", ".signature.p7s", "NET.icon.png", "NET_Library_EULA_ENU.txt", "lib/net45/System.Web.Http.Owin.dll", "lib/net45/System.Web.Http.Owin.xml", "microsoft.aspnet.webapi.owin.5.3.0.nupkg.sha512", "microsoft.aspnet.webapi.owin.nuspec"]}, "Microsoft.AspNet.WebApi.OwinSelfHost/5.3.0": {"sha512": "a/3+IJGF3N1LYF6A9esGq4xR7SIl+fpLvcgC0PrQuTtl5k1W4FLtC/qOGLi6n46QgaKf18jsm0sknImRkyuZgg==", "type": "package", "path": "microsoft.aspnet.webapi.owinselfhost/5.3.0", "files": [".nupkg.metadata", ".signature.p7s", "NET.icon.png", "NET_Library_EULA_ENU.txt", "microsoft.aspnet.webapi.owinselfhost.5.3.0.nupkg.sha512", "microsoft.aspnet.webapi.owinselfhost.nuspec"]}, "Microsoft.IO.RecyclableMemoryStream/2.3.2": {"sha512": "Oh1qXXFdJFcHozvb4H6XYLf2W0meZFuG0A+TfapFPj9z5fd4vxiARGEhAaLj/6XWQaMYIv4SH/9Q6H78Hw0E2Q==", "type": "package", "path": "microsoft.io.recyclablememorystream/2.3.2", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.IO.RecyclableMemoryStream.dll", "lib/net462/Microsoft.IO.RecyclableMemoryStream.xml", "lib/net5.0/Microsoft.IO.RecyclableMemoryStream.dll", "lib/net5.0/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netcoreapp2.1/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netcoreapp2.1/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netstandard2.1/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netstandard2.1/Microsoft.IO.RecyclableMemoryStream.xml", "microsoft.io.recyclablememorystream.2.3.2.nupkg.sha512", "microsoft.io.recyclablememorystream.nuspec"]}, "Microsoft.Owin/4.2.2": {"sha512": "jt410l/8dvCIguRdU7dupYdm4kGLepVdD8EOTKU4nYZcLRrn6kQYqI6pbJOTJp7Vlm/T2WUF5bzyKK2z29xtjg==", "type": "package", "path": "microsoft.owin/4.2.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net45/Microsoft.Owin.dll", "lib/net45/Microsoft.Owin.xml", "microsoft.owin.4.2.2.nupkg.sha512", "microsoft.owin.nuspec"]}, "Microsoft.Owin.Host.HttpListener/4.2.2": {"sha512": "Kl1A0sBzfMD3qvX6XcGU0FopN6POFFRpIEQnKIAbvsShadIG9/UxgDdHVlX/IzFHXwIHfY59Ae4RGDVKYNvIqQ==", "type": "package", "path": "microsoft.owin.host.httplistener/4.2.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net45/Microsoft.Owin.Host.HttpListener.dll", "lib/net45/Microsoft.Owin.Host.HttpListener.xml", "microsoft.owin.host.httplistener.4.2.2.nupkg.sha512", "microsoft.owin.host.httplistener.nuspec"]}, "Microsoft.Owin.Hosting/4.2.2": {"sha512": "KsupM0TNPUfLn1uHvQy22IX6VWE+wi7C2shseSnhO9JYFxgwWcsSmjxrRpw+xcD+4hA3O280tBxfZ6T+kJuhjg==", "type": "package", "path": "microsoft.owin.hosting/4.2.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net45/Microsoft.Owin.Hosting.dll", "lib/net45/Microsoft.Owin.Hosting.xml", "microsoft.owin.hosting.4.2.2.nupkg.sha512", "microsoft.owin.hosting.nuspec"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "Newtonsoft.Json.Bson/1.0.2": {"sha512": "QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "type": "package", "path": "newtonsoft.json.bson/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net45/Newtonsoft.Json.Bson.dll", "lib/net45/Newtonsoft.Json.Bson.pdb", "lib/net45/Newtonsoft.Json.Bson.xml", "lib/netstandard1.3/Newtonsoft.Json.Bson.dll", "lib/netstandard1.3/Newtonsoft.Json.Bson.pdb", "lib/netstandard1.3/Newtonsoft.Json.Bson.xml", "lib/netstandard2.0/Newtonsoft.Json.Bson.dll", "lib/netstandard2.0/Newtonsoft.Json.Bson.pdb", "lib/netstandard2.0/Newtonsoft.Json.Bson.xml", "newtonsoft.json.bson.1.0.2.nupkg.sha512", "newtonsoft.json.bson.nuspec"]}, "NPOI/2.6.2": {"sha512": "s5lxJQ1Xy2nr3yDvoMH6og2cb2I8reIrUROf2afjKucS+pWNZG07kwITo+CCS3KaXNiPjUn1YaS1PUf8fT9cHg==", "type": "package", "path": "npoi/2.6.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "Read Me.txt", "lib/net472/NPOI.Core.dll", "lib/net472/NPOI.Core.pdb", "lib/net472/NPOI.Core.xml", "lib/net472/NPOI.OOXML.dll", "lib/net472/NPOI.OOXML.pdb", "lib/net472/NPOI.OOXML.xml", "lib/net472/NPOI.OpenXml4Net.dll", "lib/net472/NPOI.OpenXml4Net.pdb", "lib/net472/NPOI.OpenXml4Net.xml", "lib/net472/NPOI.OpenXmlFormats.dll", "lib/net472/NPOI.OpenXmlFormats.pdb", "lib/net472/NPOI.OpenXmlFormats.xml", "lib/net6.0/NPOI.Core.dll", "lib/net6.0/NPOI.Core.pdb", "lib/net6.0/NPOI.Core.xml", "lib/net6.0/NPOI.OOXML.dll", "lib/net6.0/NPOI.OOXML.pdb", "lib/net6.0/NPOI.OOXML.xml", "lib/net6.0/NPOI.OpenXml4Net.dll", "lib/net6.0/NPOI.OpenXml4Net.pdb", "lib/net6.0/NPOI.OpenXml4Net.xml", "lib/net6.0/NPOI.OpenXmlFormats.dll", "lib/net6.0/NPOI.OpenXmlFormats.pdb", "lib/net6.0/NPOI.OpenXmlFormats.xml", "lib/netstandard2.0/NPOI.Core.dll", "lib/netstandard2.0/NPOI.Core.pdb", "lib/netstandard2.0/NPOI.Core.xml", "lib/netstandard2.0/NPOI.OOXML.dll", "lib/netstandard2.0/NPOI.OOXML.pdb", "lib/netstandard2.0/NPOI.OOXML.xml", "lib/netstandard2.0/NPOI.OpenXml4Net.dll", "lib/netstandard2.0/NPOI.OpenXml4Net.pdb", "lib/netstandard2.0/NPOI.OpenXml4Net.xml", "lib/netstandard2.0/NPOI.OpenXmlFormats.dll", "lib/netstandard2.0/NPOI.OpenXmlFormats.pdb", "lib/netstandard2.0/NPOI.OpenXmlFormats.xml", "lib/netstandard2.1/NPOI.Core.dll", "lib/netstandard2.1/NPOI.Core.pdb", "lib/netstandard2.1/NPOI.Core.xml", "lib/netstandard2.1/NPOI.OOXML.dll", "lib/netstandard2.1/NPOI.OOXML.pdb", "lib/netstandard2.1/NPOI.OOXML.xml", "lib/netstandard2.1/NPOI.OpenXml4Net.dll", "lib/netstandard2.1/NPOI.OpenXml4Net.pdb", "lib/netstandard2.1/NPOI.OpenXml4Net.xml", "lib/netstandard2.1/NPOI.OpenXmlFormats.dll", "lib/netstandard2.1/NPOI.OpenXmlFormats.pdb", "lib/netstandard2.1/NPOI.OpenXmlFormats.xml", "logo/120_120.jpg", "logo/240_240.png", "logo/32_32.jpg", "logo/60_60.jpg", "npoi.2.6.2.nupkg.sha512", "npoi.nuspec"]}, "Ookii.Dialogs.Wpf/5.0.1": {"sha512": "iZNnZwqTpK0cfR0vkotncP1c025/PHlIdzc7iEKBYwhVv1V0zXp9/RT/O0kMhsmlwXqRxxbE4pdhfinbJXtjPg==", "type": "package", "path": "ookii.dialogs.wpf/5.0.1", "files": [".nupkg.metadata", ".signature.p7s", "images/icon.png", "lib/net462/Ookii.Dialogs.Wpf.dll", "lib/net462/Ookii.Dialogs.Wpf.pdb", "lib/net462/Ookii.Dialogs.Wpf.xml", "lib/net5.0-windows7.0/Ookii.Dialogs.Wpf.dll", "lib/net5.0-windows7.0/Ookii.Dialogs.Wpf.pdb", "lib/net5.0-windows7.0/Ookii.Dialogs.Wpf.xml", "lib/net6.0-windows7.0/Ookii.Dialogs.Wpf.dll", "lib/net6.0-windows7.0/Ookii.Dialogs.Wpf.pdb", "lib/net6.0-windows7.0/Ookii.Dialogs.Wpf.xml", "lib/netcoreapp3.1/Ookii.Dialogs.Wpf.dll", "lib/netcoreapp3.1/Ookii.Dialogs.Wpf.pdb", "lib/netcoreapp3.1/Ookii.Dialogs.Wpf.xml", "ookii.dialogs.wpf.5.0.1.nupkg.sha512", "ookii.dialogs.wpf.nuspec"]}, "Owin/1.0.0": {"sha512": "OseTFniKmyp76mEzOBwIKGBRS5eMoYNkMKaMXOpxx9jv88+b6mh1rSaw43vjBOItNhaLFG3d0a20PfHyibH5sw==", "type": "package", "path": "owin/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/Owin.dll", "owin.1.0.0.nupkg.sha512", "owin.nuspec"]}, "Serilog/2.10.0": {"sha512": "+QX0hmf37a0/OZLxM3wL7V6/ADvC1XihXN4Kq/p6d8lCPfgkRdiuhbWlMaFjR9Av0dy5F0+MBeDmDdRZN/YwQA==", "type": "package", "path": "serilog/2.10.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net45/Serilog.dll", "lib/net45/Serilog.xml", "lib/net46/Serilog.dll", "lib/net46/Serilog.xml", "lib/netstandard1.0/Serilog.dll", "lib/netstandard1.0/Serilog.xml", "lib/netstandard1.3/Serilog.dll", "lib/netstandard1.3/Serilog.xml", "lib/netstandard2.0/Serilog.dll", "lib/netstandard2.0/Serilog.xml", "lib/netstandard2.1/Serilog.dll", "lib/netstandard2.1/Serilog.xml", "serilog.2.10.0.nupkg.sha512", "serilog.nuspec"]}, "Serilog.Sinks.Console/4.1.0": {"sha512": "K6N5q+5fetjnJPvCmkWOpJ/V8IEIoMIB1s86OzBrbxwTyHxdx3pmz4H+8+O/Dc/ftUX12DM1aynx/dDowkwzqg==", "type": "package", "path": "serilog.sinks.console/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net45/Serilog.Sinks.Console.dll", "lib/net45/Serilog.Sinks.Console.xml", "lib/net5.0/Serilog.Sinks.Console.dll", "lib/net5.0/Serilog.Sinks.Console.xml", "lib/netstandard1.3/Serilog.Sinks.Console.dll", "lib/netstandard1.3/Serilog.Sinks.Console.xml", "lib/netstandard2.0/Serilog.Sinks.Console.dll", "lib/netstandard2.0/Serilog.Sinks.Console.xml", "serilog.sinks.console.4.1.0.nupkg.sha512", "serilog.sinks.console.nuspec"]}, "Serilog.Sinks.File/5.0.0": {"sha512": "uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "type": "package", "path": "serilog.sinks.file/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "images/icon.png", "lib/net45/Serilog.Sinks.File.dll", "lib/net45/Serilog.Sinks.File.pdb", "lib/net45/Serilog.Sinks.File.xml", "lib/net5.0/Serilog.Sinks.File.dll", "lib/net5.0/Serilog.Sinks.File.pdb", "lib/net5.0/Serilog.Sinks.File.xml", "lib/netstandard1.3/Serilog.Sinks.File.dll", "lib/netstandard1.3/Serilog.Sinks.File.pdb", "lib/netstandard1.3/Serilog.Sinks.File.xml", "lib/netstandard2.0/Serilog.Sinks.File.dll", "lib/netstandard2.0/Serilog.Sinks.File.pdb", "lib/netstandard2.0/Serilog.Sinks.File.xml", "lib/netstandard2.1/Serilog.Sinks.File.dll", "lib/netstandard2.1/Serilog.Sinks.File.pdb", "lib/netstandard2.1/Serilog.Sinks.File.xml", "serilog.sinks.file.5.0.0.nupkg.sha512", "serilog.sinks.file.nuspec"]}, "SharpZipLib/1.4.2": {"sha512": "yjj+3zgz8zgXpiiC3ZdF/iyTBbz2fFvMxZFEBPUcwZjIvXOf37Ylm+K58hqMfIBt5JgU/Z2uoUS67JmTLe973A==", "type": "package", "path": "sharpziplib/1.4.2", "files": [".nupkg.metadata", ".signature.p7s", "images/sharpziplib-nuget-256x256.png", "lib/net6.0/ICSharpCode.SharpZipLib.dll", "lib/net6.0/ICSharpCode.SharpZipLib.pdb", "lib/net6.0/ICSharpCode.SharpZipLib.xml", "lib/netstandard2.0/ICSharpCode.SharpZipLib.dll", "lib/netstandard2.0/ICSharpCode.SharpZipLib.pdb", "lib/netstandard2.0/ICSharpCode.SharpZipLib.xml", "lib/netstandard2.1/ICSharpCode.SharpZipLib.dll", "lib/netstandard2.1/ICSharpCode.SharpZipLib.pdb", "lib/netstandard2.1/ICSharpCode.SharpZipLib.xml", "sharpziplib.1.4.2.nupkg.sha512", "sharpziplib.nuspec"]}, "SixLabors.Fonts/1.0.0": {"sha512": "LFQsCZlV0xlUyXAOMUo5kkSl+8zAQXXbbdwWchtk0B4o7zotZhQsQOcJUELGHdfPfm/xDAsz6hONAuV25bJaAg==", "type": "package", "path": "sixlabors.fonts/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netcoreapp3.1/SixLabors.Fonts.dll", "lib/netcoreapp3.1/SixLabors.Fonts.xml", "lib/netstandard2.0/SixLabors.Fonts.dll", "lib/netstandard2.0/SixLabors.Fonts.xml", "lib/netstandard2.1/SixLabors.Fonts.dll", "lib/netstandard2.1/SixLabors.Fonts.xml", "sixlabors.fonts.1.0.0.nupkg.sha512", "sixlabors.fonts.128.png", "sixlabors.fonts.nuspec"]}, "SixLabors.ImageSharp/2.1.4": {"sha512": "K6F39YCm/MaOYT0iw9lMa8tBcz2eF8JaNMneo0EnRrml6k+8EQNSKXqb8yJTq2HHkWLlRHZl6UKMn02YmR/G3g==", "type": "package", "path": "sixlabors.imagesharp/2.1.4", "files": [".nupkg.metadata", ".signature.p7s", "lib/net472/SixLabors.ImageSharp.dll", "lib/net472/SixLabors.ImageSharp.xml", "lib/netcoreapp2.1/SixLabors.ImageSharp.dll", "lib/netcoreapp2.1/SixLabors.ImageSharp.xml", "lib/netcoreapp3.1/SixLabors.ImageSharp.dll", "lib/netcoreapp3.1/SixLabors.ImageSharp.xml", "lib/netstandard2.0/SixLabors.ImageSharp.dll", "lib/netstandard2.0/SixLabors.ImageSharp.xml", "lib/netstandard2.1/SixLabors.ImageSharp.dll", "lib/netstandard2.1/SixLabors.ImageSharp.xml", "sixlabors.imagesharp.128.png", "sixlabors.imagesharp.2.1.4.nupkg.sha512", "sixlabors.imagesharp.nuspec"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Configuration.ConfigurationManager/6.0.0": {"sha512": "7T+m0kDSlIPTHIkPMIu6m6tV6qsMqJpvQWW2jIc2qi7sn40qxFo0q+7mEQAhMPXZHMKnWrnv47ntGlM/ejvw3g==", "type": "package", "path": "system.configuration.configurationmanager/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Configuration.ConfigurationManager.dll", "lib/net461/System.Configuration.ConfigurationManager.xml", "lib/net6.0/System.Configuration.ConfigurationManager.dll", "lib/net6.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "runtimes/win/lib/net461/System.Configuration.ConfigurationManager.dll", "runtimes/win/lib/net461/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.6.0.0.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.1.1": {"sha512": "YkOfl8PsmWT4ASkkEFFlfajgwomK8VnhwOIx0JEego69Tw5IqXjbzUBwNKcE5KprqlK92ZCYT56nQwmyEv45Ug==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.1.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net461/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "lib/net462/System.Runtime.CompilerServices.Unsafe.dll", "lib/net462/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/net7.0/_._", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.1.1.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec"]}, "System.Security.AccessControl/6.0.0": {"sha512": "AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "type": "package", "path": "system.security.accesscontrol/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.AccessControl.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/net6.0/System.Security.AccessControl.dll", "lib/net6.0/System.Security.AccessControl.xml", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/net6.0/System.Security.AccessControl.dll", "runtimes/win/lib/net6.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netstandard2.0/System.Security.AccessControl.xml", "system.security.accesscontrol.6.0.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Xml/6.0.1": {"sha512": "5e5bI28T0x73AwTsbuFP4qSRzthmU2C0Gqgg3AZ3KTxmSyA+Uhk31puA3srdaeWaacVnHhLdJywCzqOiEpbO/w==", "type": "package", "path": "system.security.cryptography.xml/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Xml.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.Cryptography.Xml.dll", "lib/net461/System.Security.Cryptography.Xml.xml", "lib/net6.0/System.Security.Cryptography.Xml.dll", "lib/net6.0/System.Security.Cryptography.Xml.xml", "lib/netstandard2.0/System.Security.Cryptography.Xml.dll", "lib/netstandard2.0/System.Security.Cryptography.Xml.xml", "runtimes/win/lib/net461/System.Security.Cryptography.Xml.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Xml.xml", "system.security.cryptography.xml.6.0.1.nupkg.sha512", "system.security.cryptography.xml.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Permissions/6.0.0": {"sha512": "T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "type": "package", "path": "system.security.permissions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.Permissions.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.Permissions.dll", "lib/net461/System.Security.Permissions.xml", "lib/net5.0/System.Security.Permissions.dll", "lib/net5.0/System.Security.Permissions.xml", "lib/net6.0/System.Security.Permissions.dll", "lib/net6.0/System.Security.Permissions.xml", "lib/netcoreapp3.1/System.Security.Permissions.dll", "lib/netcoreapp3.1/System.Security.Permissions.xml", "lib/netstandard2.0/System.Security.Permissions.dll", "lib/netstandard2.0/System.Security.Permissions.xml", "runtimes/win/lib/net461/System.Security.Permissions.dll", "runtimes/win/lib/net461/System.Security.Permissions.xml", "system.security.permissions.6.0.0.nupkg.sha512", "system.security.permissions.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Principal.Windows/5.0.0": {"sha512": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "type": "package", "path": "system.security.principal.windows/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.5.0.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encoding.CodePages/5.0.0": {"sha512": "NyscU59xX6Uo91qvhOs2Ccho3AR2TnZPomo1Z0K6YpyztBPM/A5VbkzOO19sy3A3i1TtEnTxA7bCe3Us+r5MWg==", "type": "package", "path": "system.text.encoding.codepages/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Text.Encoding.CodePages.dll", "lib/net461/System.Text.Encoding.CodePages.dll", "lib/net461/System.Text.Encoding.CodePages.xml", "lib/netstandard1.3/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/netcoreapp2.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netcoreapp2.0/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/netstandard1.3/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "system.text.encoding.codepages.5.0.0.nupkg.sha512", "system.text.encoding.codepages.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Alsi.App/1.0.0": {"type": "project", "path": "../../Alsi.Common/Alsi.App/Alsi.App.csproj", "msbuildProject": "../../Alsi.Common/Alsi.App/Alsi.App.csproj"}, "Alsi.App.Desktop/1.0.0": {"type": "project", "path": "../../Alsi.Common/Alsi.App.Desktop/Alsi.App.Desktop.csproj", "msbuildProject": "../../Alsi.Common/Alsi.App.Desktop/Alsi.App.Desktop.csproj"}, "Alsi.Common.Log/1.0.0": {"type": "project", "path": "../../Alsi.Common/Alsi.Common.Log/Alsi.Common.Log.csproj", "msbuildProject": "../../Alsi.Common/Alsi.Common.Log/Alsi.Common.Log.csproj"}, "Alsi.Common.Parsers/1.0.0": {"type": "project", "path": "../../Alsi.Common/Alsi.Common.Parsers/Alsi.Common.Parsers.csproj", "msbuildProject": "../../Alsi.Common/Alsi.Common.Parsers/Alsi.Common.Parsers.csproj"}, "Alsi.Common.Utils/1.0.0": {"type": "project", "path": "../../Alsi.Common/Alsi.Common.Utils/Alsi.Common.Utils.csproj", "msbuildProject": "../../Alsi.Common/Alsi.Common.Utils/Alsi.Common.Utils.csproj"}, "Alsi.Tab.Kit.Core/1.0.0": {"type": "project", "path": "../Alsi.Tab.Kit.Core/Alsi.Tab.Kit.Core.csproj", "msbuildProject": "../Alsi.Tab.Kit.Core/Alsi.Tab.Kit.Core.csproj"}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.6.2": ["Alsi.App >= 1.0.0", "Alsi.App.Desktop >= 1.0.0", "Alsi.Common.Parsers >= 1.0.0", "Alsi.Common.Utils >= 1.0.0", "Alsi.Tab.Kit.Core >= 1.0.0", "AutoMapper >= 8.1.1", "Microsoft.AspNet.WebApi.Owin >= 5.3.0", "Newtonsoft.Json >= 13.0.3", "System.Runtime.CompilerServices.Unsafe >= 6.1.1"]}, "packageFolders": {"D:\\nuget_packages": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\Alsi.Tab.Kit.Web.csproj", "projectName": "Alsi.Tab.Kit.Web", "projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\Alsi.Tab.Kit.Web.csproj", "packagesPath": "D:\\nuget_packages", "outputPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net462"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net462": {"projectReferences": {"D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Desktop\\Alsi.App.Desktop.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Desktop\\Alsi.App.Desktop.csproj"}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\Alsi.App.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\Alsi.App.csproj"}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Parsers\\Alsi.Common.Parsers.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Parsers\\Alsi.Common.Parsers.csproj"}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj"}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj"}}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net462": {"dependencies": {"AutoMapper": {"target": "Package", "version": "[8.1.1, )"}, "Microsoft.AspNet.WebApi.Owin": {"target": "Package", "version": "[5.3.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Runtime.CompilerServices.Unsafe": {"target": "Package", "version": "[6.1.1, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}