{"version": 3, "file": "js/log-converter.68a4e437.js", "mappings": "0LAEA,MAAMA,EAAa,CCDZC,MAAM,iBDEPC,EAAa,CCGVD,MAAM,qBDFTE,EAAa,CCGRF,MAAM,cDFXG,EAAa,CCGNH,MAAM,cAAcI,MAAA,eDC3BC,EAAa,CCWRL,MAAM,cDVXM,EAAa,CCWNN,MAAM,eDVbO,EAAa,CCkBNP,MAAM,eDjBbQ,EAAa,CACjBC,IAAK,ECqBMT,MAAM,eDlBbU,EAAa,CC0BRV,MAAM,kBDzBXW,EAAc,CAClBF,IAAK,ECmCoBT,MAAM,oBDhC3BY,EAAc,CCoCTZ,MAAM,qBDnCXa,EAAc,CAClBJ,IAAK,ECqC2ET,MAAM,iBDlClFc,EAAc,CCoCLd,MAAM,sBDnCfe,EAAc,CCqCDf,MAAM,aDpCnBgB,EAAc,CCsCDhB,MAAM,eDrCnBiB,EAAc,CC6CXjB,MAAM,cD5CTkB,EAAc,CAAET,IAAK,GACrBU,EAAc,CAAEV,IAAK,GACrBW,EAAc,CAClBX,IAAK,EC8CyBT,MAAM,qBD1ChC,SAAUqB,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAMC,GAAuBC,EAAAA,EAAAA,IAAkB,aACzCC,GAAsBD,EAAAA,EAAAA,IAAkB,YACxCE,GAAsBF,EAAAA,EAAAA,IAAkB,YACxCG,GAA4BH,EAAAA,EAAAA,IAAkB,kBAC9CI,GAAuBJ,EAAAA,EAAAA,IAAkB,aACzCK,GAA6BL,EAAAA,EAAAA,IAAkB,mBAC/CM,GAAyBN,EAAAA,EAAAA,IAAkB,eAEjD,OAAQO,EAAAA,EAAAA,OC7CRC,EAAAA,EAAAA,IAkFM,MAlFNtC,EAkFM,CDpCJwB,EAAO,MAAQA,EAAO,KC5CtBe,EAAAA,EAAAA,IAAqC,OAAhCtC,MAAM,aAAY,YAAQ,KAG/BsC,EAAAA,EAAAA,IAiEM,MAjENrC,EAiEM,EAhEJqC,EAAAA,EAAAA,IAWM,MAXNpC,EAWM,EAVJoC,EAAAA,EAAAA,IASM,MATNnC,EASM,CDkCJoB,EAAO,KAAOA,EAAO,IC1CrBe,EAAAA,EAAAA,IAA0E,SAAnEtC,MAAM,eAAeI,MAAA,0BAA6B,aAAS,KAClEmC,EAAAA,EAAAA,IAMWT,EAAA,CDwCTU,WC9CiBlB,EAAAmB,SAASC,SD+C1B,sBAAuBnB,EAAO,KAAOA,EAAO,GAAMoB,GC/CjCrB,EAAAmB,SAASC,SAAQC,GAAEC,YAAY,iCAAkCC,OAAMvB,EAAAwB,aDkDvF,CCjDUC,QAAMC,EAAAA,EAAAA,IACf,IAEY,EAFZT,EAAAA,EAAAA,IAEYX,EAAA,CAFAqB,QAAO3B,EAAA4B,YAAU,CDmD3BC,SAASH,EAAAA,EAAAA,ICnDoB,IAE/BzB,EAAA,KAAAA,EAAA,KDkDI6B,EAAAA,EAAAA,ICpD2B,kBDsD7BC,EAAG,EACHC,GAAI,CAAC,IACJ,EAAG,CAAC,cAETD,EAAG,GACF,EAAG,CAAC,aAAc,gBCnDzBf,EAAAA,EAAAA,IAmBM,MAnBNjC,EAmBM,EAlBJiC,EAAAA,EAAAA,IAMM,MANNhC,EAMM,CDiDJiB,EAAO,KAAOA,EAAO,ICtDrBe,EAAAA,EAAAA,IAAwC,SAAjCtC,MAAM,gBAAe,QAAI,KAChCuC,EAAAA,EAAAA,IAGiBP,EAAA,CDoDfQ,WCvDuBlB,EAAAiC,eAAeC,aDwDtC,sBAAuBjC,EAAO,KAAOA,EAAO,GAAMoB,GCxD3BrB,EAAAiC,eAAeC,aAAYb,IDyDjD,CACDQ,SAASH,EAAAA,EAAAA,ICzDT,IAAmC,EAAnCT,EAAAA,EAAAA,IAAmCR,EAAA,CAAxB0B,MAAO,GAAC,CD2DfN,SAASH,EAAAA,EAAAA,IC3DQ,IAAGzB,EAAA,KAAAA,EAAA,KD4DlB6B,EAAAA,EAAAA,IC5De,UD8DjBC,EAAG,EACHC,GAAI,CAAC,MC9DTf,EAAAA,EAAAA,IAAmCR,EAAA,CAAxB0B,MAAO,GAAC,CDiEfN,SAASH,EAAAA,EAAAA,ICjEQ,IAAGzB,EAAA,KAAAA,EAAA,KDkElB6B,EAAAA,EAAAA,IClEe,UDoEjBC,EAAG,EACHC,GAAI,CAAC,OAGTD,EAAG,GACF,EAAG,CAAC,kBCrETf,EAAAA,EAAAA,IAGM,MAHN/B,EAGM,CDqEJgB,EAAO,KAAOA,EAAO,ICvErBe,EAAAA,EAAAA,IAAwC,SAAjCtC,MAAM,gBAAe,QAAI,KAChCuC,EAAAA,EAAAA,IAA0EN,EAAA,CDwExEO,WCxEkBlB,EAAAiC,eAAeG,YDyEjC,sBAAuBnC,EAAO,KAAOA,EAAO,GAAMoB,GCzEhCrB,EAAAiC,eAAeG,YAAWf,GAAGgB,SAAQrC,EAAAsC,eD2EtD,KAAM,EAAG,CAAC,aAAc,eCxEEtC,EAAAiC,eAAeG,cD2EzCtB,EAAAA,EAAAA,OC3ELC,EAAAA,EAAAA,IAIM,MAJN7B,EAIM,CDwEAe,EAAO,MAAQA,EAAO,KC3E1Be,EAAAA,EAAAA,IAAyC,SAAlCtC,MAAM,gBAAe,SAAK,KACjCuC,EAAAA,EAAAA,IAC8BL,EAAA,CD2ExBM,WC5EoBlB,EAAAuC,eD6EpB,sBAAuBtC,EAAO,KAAOA,EAAO,GAAMoB,GC7E9BrB,EAAAuC,eAAclB,GAAGmB,IAAK,EAAIC,IAAK,IAAMJ,SAAQrC,EAAA0C,mBACrEhE,MAAM,qBDiFD,KAAM,EAAG,CAAC,aAAc,iBAE7BiE,EAAAA,EAAAA,IAAoB,IAAI,MC9E9B3B,EAAAA,EAAAA,IAQM,MARN5B,EAQM,EAPJ6B,EAAAA,EAAAA,IAEYX,EAAA,CAFDsC,KAAK,UAAWC,UAAW7C,EAAAmB,SAASC,SAAWO,QAAO3B,EAAA8C,aAAeC,QAAS/C,EAAAgD,cDqFtF,CACDnB,SAASH,EAAAA,EAAAA,ICtF4F,IAEvGzB,EAAA,MAAAA,EAAA,MDqFI6B,EAAAA,EAAAA,ICvFmG,aDyFrGC,EAAG,EACHC,GAAI,CAAC,KACJ,EAAG,CAAC,WAAY,UAAW,aCvF9Bf,EAAAA,EAAAA,IAEYX,EAAA,CAFDsC,KAAK,SAAUC,UAAW7C,EAAAgD,aAAerB,QAAO3B,EAAAiD,eD4FxD,CACDpB,SAASH,EAAAA,EAAAA,IC7F+D,IAE1EzB,EAAA,MAAAA,EAAA,MD4FI6B,EAAAA,EAAAA,IC9FsE,aDgGxEC,EAAG,EACHC,GAAI,CAAC,KACJ,EAAG,CAAC,WAAY,cC5FVhC,EAAAkD,WD+FNpC,EAAAA,EAAAA,OC/FLC,EAAAA,EAAAA,IAiBM,MAjBN1B,EAiBM,CD+EAY,EAAO,MAAQA,EAAO,KC/F1Be,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRC,EAAAA,EAAAA,IAC0DJ,EAAA,CAD5CsC,WAAYnD,EAAAkD,SAASE,0BAChCC,OAAQrD,EAAAkD,SAASI,YAAc,UAAY,UDiGvC,KAAM,EAAG,CAAC,aAAc,YChG/BtC,EAAAA,EAAAA,IAAgE,IAAhE1B,GAAgEiE,EAAAA,EAAAA,IAAhCvD,EAAAkD,SAASM,kBAAgB,GAG9CxD,EAAAkD,SAASO,gBAAkBzD,EAAAkD,SAASO,eAAeC,OAAS,IDgG9D5C,EAAAA,EAAAA,OChGTC,EAAAA,EAAAA,IASM,MATNxB,EASM,CDwFIU,EAAO,MAAQA,EAAO,KChG9Be,EAAAA,EAAAA,IAAe,UAAX,UAAM,KACVA,EAAAA,EAAAA,IAMM,MANNxB,EAMM,GD2FKsB,EAAAA,EAAAA,KAAW,IChGpBC,EAAAA,EAAAA,IAIM4C,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAJ+B5D,EAAAkD,SAASO,eAAc,CAA/CI,EAAcC,MDiGThD,EAAAA,EAAAA,OCjGlBC,EAAAA,EAAAA,IAIM,OAJyD5B,IAAK2E,EAAOpF,MAAM,sBDoGpE,ECnGXsC,EAAAA,EAAAA,IAAwD,MAAxDvB,GAAwD8D,EAAAA,EAAAA,IAA9BM,EAAaE,UAAQ,IAC/C9C,EAAAA,EAAAA,IAAmHJ,EAAA,CAArGsC,WAAYU,EAAaG,mBAAqB,aAAW,EAAOC,KAAK,QAAQnF,MAAA,iBDyG9E,KAAM,EAAG,CAAC,gBCxGvBkC,EAAAA,EAAAA,IAAuE,MAAvEtB,GAAuE6D,EAAAA,EAAAA,IAA3CvD,EAAAkE,cAAcL,EAAaR,SAAM,OD2GnD,YAGRV,EAAAA,EAAAA,IAAoB,IAAI,OAE9BA,EAAAA,EAAAA,IAAoB,IAAI,MCxG9B3B,EAAAA,EAAAA,IAQM,MARNrB,EAQM,CAPQK,EAAAmB,SAASC,WD2GhBN,EAAAA,EAAAA,OC3GLC,EAAAA,EAAAA,IAEO,OAAAnB,EAFwB,eACnB2D,EAAAA,EAAAA,IAAGvD,EAAAmB,SAASC,UAAQ,KD2G5BuB,EAAAA,EAAAA,IAAoB,IAAI,GCzGf3C,EAAAmB,SAASC,UD4GlBuB,EAAAA,EAAAA,IAAoB,IAAI,KADvB7B,EAAAA,EAAAA,OC3GLC,EAAAA,EAAAA,IAAiD,OAAAlB,EAAjB,eACpBG,EAAAgD,eD6GPlC,EAAAA,EAAAA,OC7GLC,EAAAA,EAAAA,IAEO,OAFPjB,EAAoD,YAC3CyD,EAAAA,EAAAA,IAAGvD,EAAAkD,UAAUE,2BAA6B,GAAI,KACvD,KD4GIT,EAAAA,EAAAA,IAAoB,IAAI,MAGlC,C,uBChGA,GAAewB,EAAAA,EAAAA,IAAgB,CAC7BC,KAAM,mBACNC,KAAAA,GAEE,MAAMlD,GAAWmD,EAAAA,EAAAA,IAAI,CAAElD,SAAU,KAG3Ba,GAAiBqC,EAAAA,EAAAA,IAA2B,CAChDC,eAAgB,GAChBrC,aAAcsC,EAAAA,GAAcC,IAC5BrC,aAAa,EACbG,eAAgB,IAGZA,GAAiB+B,EAAAA,EAAAA,IAAI,GACrBI,GAAgBJ,EAAAA,EAAAA,IAAmB,MACnCpB,GAAWoB,EAAAA,EAAAA,IAA4B,MAGvCtB,GAAesB,EAAAA,EAAAA,KAAI,GAEzB,IAAIK,EAA+B,KAGnC,MAAM/C,EAAagD,UACjB,MAAMC,QAAiBC,EAAAA,GAAOC,eAAenD,aACvCoD,EAASH,EAASI,KAEpBD,IACF7D,EAASgB,MAAMf,SAAW4D,EAC1B/C,EAAeE,MAAMoC,eAAiBS,IAIpCxD,EAAcoD,UACbzD,EAASgB,MAAMf,WACpBa,EAAeE,MAAMoC,eAAiBpD,EAASgB,MAAMf,WAIjDkB,EAAgBA,KAChBL,EAAeE,MAAMC,cACvBG,EAAeJ,MAAQ,EACvBF,EAAeE,MAAMI,eAAiB,IAKpCG,EAAqBA,KACzBT,EAAeE,MAAMI,eAAiBA,EAAeJ,OAGjDW,EAAe8B,UACnB5B,EAAab,OAAQ,EACrB,IACE,MAAM0C,QAAiBC,EAAAA,GAAOC,eAAejC,aAAab,EAAeE,OACzEuC,EAAcvC,MAAQ0C,EAASI,KAG/BC,G,CACA,MAAOC,GACPC,QAAQD,MAAM,UAAWA,GACzBnC,EAAab,OAAQ,C,GAInB+C,EAAuBA,KACtBR,EAAcvC,QAEnBwC,EAAgBU,OAAOC,YAAYV,UACjC,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,eAAeQ,YAAYb,EAAcvC,OACvEe,EAASf,MAAQ0C,EAASI,KAEtB/B,EAASf,OAAOmB,cAClBkC,IACAxC,EAAab,OAAQ,E,CAEvB,MAAOgD,GACPC,QAAQD,MAAM,UAAWA,E,GAE1B,OAGCK,EAAsBA,KACtBb,IACFc,cAAcd,GACdA,EAAgB,OAId1B,EAAgB2B,UACpB,GAAKF,EAAcvC,MAEnB,UACQ2C,EAAAA,GAAOC,eAAe9B,cAAcyB,EAAcvC,OACxDqD,IACAxC,EAAab,OAAQ,C,CACrB,MAAOgD,GACPC,QAAQD,MAAM,UAAWA,E,GAIvBjB,EAAiBb,IACrB,OAAQA,GACN,KAAKqC,EAAAA,GAAcC,QAAS,MAAO,MACnC,KAAKD,EAAAA,GAAcE,WAAY,MAAO,MACtC,KAAKF,EAAAA,GAAcG,UAAW,MAAO,MACrC,KAAKH,EAAAA,GAAcI,OAAQ,MAAO,KAClC,KAAKJ,EAAAA,GAAcK,UAAW,MAAO,MACrC,QAAS,MAAO,OAQpB,OAJAC,EAAAA,EAAAA,IAAY,KACVR,MAGK,CACLrE,WACAc,iBACAM,iBACAW,WACAF,eACApB,aACAJ,cACAc,gBACAI,qBACAI,eACAG,gBACAiB,gBAEJ,I,UC7NF,MAAM+B,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASlG,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://tab-kit-web/./src/views/LogConverterView.vue?b0b0", "webpack://tab-kit-web/./src/views/LogConverterView.vue", "webpack://tab-kit-web/./src/views/LogConverterView.vue?5b3b"], "sourcesContent": ["import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment } from \"vue\"\n\nconst _hoisted_1 = { class: \"log-converter\" }\nconst _hoisted_2 = { class: \"converter-content\" }\nconst _hoisted_3 = { class: \"config-row\" }\nconst _hoisted_4 = {\n  class: \"config-item\",\n  style: {\"width\":\"60%\"}\n}\nconst _hoisted_5 = { class: \"config-row\" }\nconst _hoisted_6 = { class: \"config-item\" }\nconst _hoisted_7 = { class: \"config-item\" }\nconst _hoisted_8 = {\n  key: 0,\n  class: \"config-item\"\n}\nconst _hoisted_9 = { class: \"action-buttons\" }\nconst _hoisted_10 = {\n  key: 0,\n  class: \"progress-section\"\n}\nconst _hoisted_11 = { class: \"current-operation\" }\nconst _hoisted_12 = {\n  key: 0,\n  class: \"file-progress\"\n}\nconst _hoisted_13 = { class: \"file-progress-list\" }\nconst _hoisted_14 = { class: \"file-name\" }\nconst _hoisted_15 = { class: \"file-status\" }\nconst _hoisted_16 = { class: \"status-bar\" }\nconst _hoisted_17 = { key: 0 }\nconst _hoisted_18 = { key: 1 }\nconst _hoisted_19 = {\n  key: 2,\n  class: \"processing-status\"\n}\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_input = _resolveComponent(\"el-input\")!\n  const _component_el_radio = _resolveComponent(\"el-radio\")!\n  const _component_el_radio_group = _resolveComponent(\"el-radio-group\")!\n  const _component_el_switch = _resolveComponent(\"el-switch\")!\n  const _component_el_input_number = _resolveComponent(\"el-input-number\")!\n  const _component_el_progress = _resolveComponent(\"el-progress\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _cache[15] || (_cache[15] = _createElementVNode(\"div\", { class: \"title-bar\" }, \"Log 转换工具\", -1)),\n    _createElementVNode(\"div\", _hoisted_2, [\n      _createElementVNode(\"div\", _hoisted_3, [\n        _createElementVNode(\"div\", _hoisted_4, [\n          _cache[5] || (_cache[5] = _createElementVNode(\"label\", {\n            class: \"config-label\",\n            style: {\"margin-bottom\":\"10px\"}\n          }, \"Log 文件路径:\", -1)),\n          _createVNode(_component_el_input, {\n            modelValue: _ctx.fileForm.filePath,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((_ctx.fileForm.filePath) = $event)),\n            placeholder: \"请输入或选择一个 Log 文件，支持格式：.asc .blf\",\n            onBlur: _ctx.analyzeFile\n          }, {\n            append: _withCtx(() => [\n              _createVNode(_component_el_button, { onClick: _ctx.selectFile }, {\n                default: _withCtx(() => _cache[4] || (_cache[4] = [\n                  _createTextVNode(\" 选择 Log 文件 \")\n                ])),\n                _: 1,\n                __: [4]\n              }, 8, [\"onClick\"])\n            ]),\n            _: 1\n          }, 8, [\"modelValue\", \"onBlur\"])\n        ])\n      ]),\n      _createElementVNode(\"div\", _hoisted_5, [\n        _createElementVNode(\"div\", _hoisted_6, [\n          _cache[8] || (_cache[8] = _createElementVNode(\"label\", { class: \"config-label\" }, \"目标格式\", -1)),\n          _createVNode(_component_el_radio_group, {\n            modelValue: _ctx.processRequest.targetFormat,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = ($event: any) => ((_ctx.processRequest.targetFormat) = $event))\n          }, {\n            default: _withCtx(() => [\n              _createVNode(_component_el_radio, { value: 1 }, {\n                default: _withCtx(() => _cache[6] || (_cache[6] = [\n                  _createTextVNode(\"ASC\")\n                ])),\n                _: 1,\n                __: [6]\n              }),\n              _createVNode(_component_el_radio, { value: 2 }, {\n                default: _withCtx(() => _cache[7] || (_cache[7] = [\n                  _createTextVNode(\"BLF\")\n                ])),\n                _: 1,\n                __: [7]\n              })\n            ]),\n            _: 1\n          }, 8, [\"modelValue\"])\n        ]),\n        _createElementVNode(\"div\", _hoisted_7, [\n          _cache[9] || (_cache[9] = _createElementVNode(\"label\", { class: \"config-label\" }, \"启用分割\", -1)),\n          _createVNode(_component_el_switch, {\n            modelValue: _ctx.processRequest.enableSplit,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = ($event: any) => ((_ctx.processRequest.enableSplit) = $event)),\n            onChange: _ctx.onSplitToggle\n          }, null, 8, [\"modelValue\", \"onChange\"])\n        ]),\n        (_ctx.processRequest.enableSplit)\n          ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [\n              _cache[10] || (_cache[10] = _createElementVNode(\"label\", { class: \"config-label\" }, \"分割文件数\", -1)),\n              _createVNode(_component_el_input_number, {\n                modelValue: _ctx.splitFileCount,\n                \"onUpdate:modelValue\": _cache[3] || (_cache[3] = ($event: any) => ((_ctx.splitFileCount) = $event)),\n                min: 1,\n                max: 100,\n                onChange: _ctx.onSplitCountChange,\n                class: \"split-count-input\"\n              }, null, 8, [\"modelValue\", \"onChange\"])\n            ]))\n          : _createCommentVNode(\"\", true)\n      ]),\n      _createElementVNode(\"div\", _hoisted_9, [\n        _createVNode(_component_el_button, {\n          type: \"primary\",\n          disabled: !_ctx.fileForm.filePath,\n          onClick: _ctx.startProcess,\n          loading: _ctx.isProcessing\n        }, {\n          default: _withCtx(() => _cache[11] || (_cache[11] = [\n            _createTextVNode(\" 开始转换 \")\n          ])),\n          _: 1,\n          __: [11]\n        }, 8, [\"disabled\", \"onClick\", \"loading\"]),\n        _createVNode(_component_el_button, {\n          type: \"danger\",\n          disabled: !_ctx.isProcessing,\n          onClick: _ctx.cancelProcess\n        }, {\n          default: _withCtx(() => _cache[12] || (_cache[12] = [\n            _createTextVNode(\" 取消转换 \")\n          ])),\n          _: 1,\n          __: [12]\n        }, 8, [\"disabled\", \"onClick\"])\n      ]),\n      (_ctx.progress)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [\n            _cache[14] || (_cache[14] = _createElementVNode(\"h4\", null, \"转换进度\", -1)),\n            _createVNode(_component_el_progress, {\n              percentage: _ctx.progress.overallProgressPercentage,\n              status: _ctx.progress.isCompleted ? 'success' : 'active'\n            }, null, 8, [\"percentage\", \"status\"]),\n            _createElementVNode(\"p\", _hoisted_11, _toDisplayString(_ctx.progress.currentOperation), 1),\n            (_ctx.progress.fileProgresses && _ctx.progress.fileProgresses.length > 0)\n              ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [\n                  _cache[13] || (_cache[13] = _createElementVNode(\"h5\", null, \"文件处理详情\", -1)),\n                  _createElementVNode(\"div\", _hoisted_13, [\n                    (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.progress.fileProgresses, (fileProgress, index) => {\n                      return (_openBlock(), _createElementBlock(\"div\", {\n                        key: index,\n                        class: \"file-progress-item\"\n                      }, [\n                        _createElementVNode(\"div\", _hoisted_14, _toDisplayString(fileProgress.fileName), 1),\n                        _createVNode(_component_el_progress, {\n                          percentage: fileProgress.progressPercentage,\n                          \"show-text\": false,\n                          size: \"small\",\n                          style: {\"width\":\"100px\"}\n                        }, null, 8, [\"percentage\"]),\n                        _createElementVNode(\"div\", _hoisted_15, _toDisplayString(_ctx.getStatusName(fileProgress.status)), 1)\n                      ]))\n                    }), 128))\n                  ])\n                ]))\n              : _createCommentVNode(\"\", true)\n          ]))\n        : _createCommentVNode(\"\", true)\n    ]),\n    _createElementVNode(\"div\", _hoisted_16, [\n      (_ctx.fileForm.filePath)\n        ? (_openBlock(), _createElementBlock(\"span\", _hoisted_17, \" 文件: 已选择 - \" + _toDisplayString(_ctx.fileForm.filePath), 1))\n        : _createCommentVNode(\"\", true),\n      (!_ctx.fileForm.filePath)\n        ? (_openBlock(), _createElementBlock(\"span\", _hoisted_18, \"请选择 Log 文件\"))\n        : _createCommentVNode(\"\", true),\n      (_ctx.isProcessing)\n        ? (_openBlock(), _createElementBlock(\"span\", _hoisted_19, \" 转换中... \" + _toDisplayString(_ctx.progress?.overallProgressPercentage || 0) + \"% \", 1))\n        : _createCommentVNode(\"\", true)\n    ])\n  ]))\n}", "<template>\n  <div class=\"log-converter\">\n    <!-- 标题栏 -->\n    <div class=\"title-bar\">Log 转换工具</div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"converter-content\">\n      <div class=\"config-row\">\n        <div class=\"config-item\" style=\"width:60%;\">\n          <label class=\"config-label\" style=\"margin-bottom: 10px;\">Log 文件路径:</label>\n          <el-input v-model=\"fileForm.filePath\" placeholder=\"请输入或选择一个 Log 文件，支持格式：.asc .blf\" @blur=\"analyzeFile\">\n            <template #append>\n              <el-button @click=\"selectFile\">\n                选择 Log 文件\n              </el-button>\n            </template>\n          </el-input>\n        </div>\n      </div>\n\n      <div class=\"config-row\">\n        <div class=\"config-item\">\n          <label class=\"config-label\">目标格式</label>\n          <el-radio-group v-model=\"processRequest.targetFormat\">\n            <el-radio :value=\"1\">ASC</el-radio>\n            <el-radio :value=\"2\">BLF</el-radio>\n          </el-radio-group>\n        </div>\n\n        <div class=\"config-item\">\n          <label class=\"config-label\">启用分割</label>\n          <el-switch v-model=\"processRequest.enableSplit\" @change=\"onSplitToggle\" />\n        </div>\n\n        <div class=\"config-item\" v-if=\"processRequest.enableSplit\">\n          <label class=\"config-label\">分割文件数</label>\n          <el-input-number v-model=\"splitFileCount\" :min=\"1\" :max=\"100\" @change=\"onSplitCountChange\"\n            class=\"split-count-input\" />\n        </div>\n      </div>\n\n      <!-- 操作控制区域 -->\n      <div class=\"action-buttons\">\n        <el-button type=\"primary\" :disabled=\"!fileForm.filePath\" @click=\"startProcess\" :loading=\"isProcessing\">\n          开始转换\n        </el-button>\n\n        <el-button type=\"danger\" :disabled=\"!isProcessing\" @click=\"cancelProcess\">\n          取消转换\n        </el-button>\n      </div>\n\n      <!-- 进度显示 -->\n      <div v-if=\"progress\" class=\"progress-section\">\n        <h4>转换进度</h4>\n        <el-progress :percentage=\"progress.overallProgressPercentage\"\n          :status=\"progress.isCompleted ? 'success' : 'active'\" />\n        <p class=\"current-operation\">{{ progress.currentOperation }}</p>\n\n        <!-- 文件处理进度 -->\n        <div v-if=\"progress.fileProgresses && progress.fileProgresses.length > 0\" class=\"file-progress\">\n          <h5>文件处理详情</h5>\n          <div class=\"file-progress-list\">\n            <div v-for=\"(fileProgress, index) in progress.fileProgresses\" :key=\"index\" class=\"file-progress-item\">\n              <div class=\"file-name\">{{ fileProgress.fileName }}</div>\n              <el-progress :percentage=\"fileProgress.progressPercentage\" :show-text=\"false\" size=\"small\" style=\"width: 100px;\" />\n              <div class=\"file-status\">{{ getStatusName(fileProgress.status) }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 状态栏 -->\n    <div class=\"status-bar\">\n      <span v-if=\"fileForm.filePath\">\n        文件: 已选择 - {{ fileForm.filePath }}\n      </span>\n      <span v-if=\"!fileForm.filePath\">请选择 Log 文件</span>\n      <span v-if=\"isProcessing\" class=\"processing-status\">\n        转换中... {{ progress?.overallProgressPercentage || 0 }}%\n      </span>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, ref, onUnmounted } from \"vue\";\nimport {\n  appApi,\n  DataLogProcessRequest,\n  ProcessProgress,\n  DataLogFormat,\n  ProcessStatus\n} from \"@/api/appApi\";\n\nexport default defineComponent({\n  name: \"LogConverterView\",\n  setup() {\n    // 文件相关\n    const fileForm = ref({ filePath: '' });\n\n    // 处理请求\n    const processRequest = ref<DataLogProcessRequest>({\n      sourceFilePath: '',\n      targetFormat: DataLogFormat.Blf,\n      enableSplit: false,\n      splitFileCount: 1\n    });\n\n    const splitFileCount = ref(1);\n    const currentTaskId = ref<string | null>(null);\n    const progress = ref<ProcessProgress | null>(null);\n\n    // 状态控制\n    const isProcessing = ref(false);\n\n    let progressTimer: number | null = null;\n\n    // 文件选择\n    const selectFile = async () => {\n      const response = await appApi.dataLogConvert.selectFile();\n      const result = response.data;\n\n      if (result) {\n        fileForm.value.filePath = result;\n        processRequest.value.sourceFilePath = result;\n      }\n    };\n\n    const analyzeFile = async () => {\n      if (!fileForm.value.filePath) return;\n      processRequest.value.sourceFilePath = fileForm.value.filePath;\n    };\n\n    // 分割开关变化\n    const onSplitToggle = () => {\n      if (processRequest.value.enableSplit) {\n        splitFileCount.value = 1;\n        processRequest.value.splitFileCount = 1;\n      }\n    };\n\n    // 分割文件数变化\n    const onSplitCountChange = () => {\n      processRequest.value.splitFileCount = splitFileCount.value;\n    };\n\n    const startProcess = async () => {\n      isProcessing.value = true;\n      try {\n        const response = await appApi.dataLogConvert.startProcess(processRequest.value);\n        currentTaskId.value = response.data;\n\n        // 开始轮询进度\n        startProgressPolling();\n      } catch (error) {\n        console.error('开始处理失败:', error);\n        isProcessing.value = false;\n      }\n    };\n\n    const startProgressPolling = () => {\n      if (!currentTaskId.value) return;\n\n      progressTimer = window.setInterval(async () => {\n        try {\n          const response = await appApi.dataLogConvert.getProgress(currentTaskId.value!);\n          progress.value = response.data;\n\n          if (progress.value?.isCompleted) {\n            stopProgressPolling();\n            isProcessing.value = false;\n          }\n        } catch (error) {\n          console.error('获取进度失败:', error);\n        }\n      }, 1000);\n    };\n\n    const stopProgressPolling = () => {\n      if (progressTimer) {\n        clearInterval(progressTimer);\n        progressTimer = null;\n      }\n    };\n\n    const cancelProcess = async () => {\n      if (!currentTaskId.value) return;\n\n      try {\n        await appApi.dataLogConvert.cancelProcess(currentTaskId.value);\n        stopProgressPolling();\n        isProcessing.value = false;\n      } catch (error) {\n        console.error('取消处理失败:', error);\n      }\n    };\n\n    const getStatusName = (status: ProcessStatus): string => {\n      switch (status) {\n        case ProcessStatus.Pending: return '等待中';\n        case ProcessStatus.Processing: return '处理中';\n        case ProcessStatus.Completed: return '已完成';\n        case ProcessStatus.Failed: return '失败';\n        case ProcessStatus.Cancelled: return '已取消';\n        default: return '未知';\n      }\n    };\n\n    onUnmounted(() => {\n      stopProgressPolling();\n    });\n\n    return {\n      fileForm,\n      processRequest,\n      splitFileCount,\n      progress,\n      isProcessing,\n      selectFile,\n      analyzeFile,\n      onSplitToggle,\n      onSplitCountChange,\n      startProcess,\n      cancelProcess,\n      getStatusName\n    };\n  },\n});\n</script>\n\n<style scoped>\n.log-converter {\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  overflow: hidden;\n}\n\n.title-bar {\n  font-size: 18px;\n  margin: 0 20px;\n  padding: 10px;\n  color: var(--el-text-color-primary);\n  font-weight: bold;\n  border-bottom: solid var(--el-border-color-base) 1px;\n  flex-shrink: 0;\n  height: 60px;\n  display: flex;\n  align-items: center;\n}\n\n.converter-content {\n  flex: 1;\n  padding: 20px;\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  margin: 0 0 0 10px;\n  width: 100%;\n}\n\n.file-info {\n  background-color: #f8f9fa;\n  margin: 0;\n  border-radius: 0;\n}\n\n.file-info h4 {\n  color: #2c3e50;\n  margin-bottom: 15px;\n  font-size: 1rem;\n}\n\n.info-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 10px;\n  margin-bottom: 15px;\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n}\n\n.info-item .label {\n  color: #7f8c8d;\n  font-weight: 500;\n}\n\n.info-item .value {\n  color: #2c3e50;\n  font-weight: bold;\n}\n\n.file-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.error-info {\n  margin-top: 15px;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 10px;\n  margin-bottom: 20px;\n}\n\n.progress-section {\n  border-top: 1px solid #e9ecef;\n}\n\n.progress-section h4 {\n  color: #2c3e50;\n  margin-bottom: 15px;\n  font-size: 1rem;\n}\n\n.current-operation {\n  color: #7f8c8d;\n  margin-top: 10px;\n  font-size: 0.9rem;\n}\n\n.file-progress {\n  margin-top: 20px;\n}\n\n.file-progress h5 {\n  color: #2c3e50;\n  margin-bottom: 10px;\n  font-size: 0.9rem;\n}\n\n.file-progress-list {\n  max-height: 150px;\n  overflow-y: auto;\n}\n\n.file-progress-item {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  padding: 8px;\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n  margin-bottom: 6px;\n  background-color: #fff;\n}\n\n.file-progress-item:last-child {\n  margin-bottom: 0;\n}\n\n.file-progress-item .file-name {\n  flex: 1;\n  font-size: 0.9rem;\n  color: #2c3e50;\n  font-weight: 500;\n}\n\n.file-progress-item .file-status {\n  width: 80px;\n  font-size: 0.8rem;\n  color: #7f8c8d;\n}\n\n.completion-section {\n  margin-top: 20px;\n  padding-top: 20px;\n  border-top: 1px solid var(--el-border-color-light);\n}\n\n.status-bar {\n  margin: 0 10px;\n  height: 54px;\n  background-color: var(--el-fill-color-light);\n  padding: 10px 20px;\n  border-top: 1px solid var(--el-border-color-base);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 0.9rem;\n  color: var(--el-text-color-regular);\n  flex-shrink: 0;\n}\n\n.processing-status {\n  color: var(--el-color-primary);\n  font-weight: bold;\n}\n\n.config-row {\n  display: flex;\n  align-items: flex-start;\n  gap: 60px;\n  flex-wrap: wrap;\n}\n\n.config-item {\n  display: flex;\n  flex-direction: column;\n}\n\n.config-label {\n  font-size: 14px;\n  color: var(--el-text-color-regular);\n  font-weight: 500;\n  margin-bottom: 4px;\n}\n\n.split-count-input {\n  width: 120px;\n}\n\n@media (max-width: 768px) {\n  .converter-content {\n    padding: 10px;\n  }\n\n  .config-row {\n    flex-direction: column;\n    gap: 15px;\n  }\n\n  .config-item {\n    min-width: auto;\n    width: 100%;\n  }\n\n  .action-buttons {\n    flex-direction: column;\n  }\n\n  .file-progress-item {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 5px;\n  }\n\n  .status-bar {\n    flex-direction: column;\n    gap: 5px;\n    text-align: center;\n  }\n}\n</style>\n", "import { render } from \"./LogConverterView.vue?vue&type=template&id=60174b90&scoped=true&ts=true\"\nimport script from \"./LogConverterView.vue?vue&type=script&lang=ts\"\nexport * from \"./LogConverterView.vue?vue&type=script&lang=ts\"\n\nimport \"./LogConverterView.vue?vue&type=style&index=0&id=60174b90&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-60174b90\"]])\n\nexport default __exports__"], "names": ["_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "style", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "key", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "_component_el_button", "_resolveComponent", "_component_el_input", "_component_el_radio", "_component_el_radio_group", "_component_el_switch", "_component_el_input_number", "_component_el_progress", "_openBlock", "_createElementBlock", "_createElementVNode", "_createVNode", "modelValue", "fileForm", "filePath", "$event", "placeholder", "onBlur", "analyzeFile", "append", "_withCtx", "onClick", "selectFile", "default", "_createTextVNode", "_", "__", "processRequest", "targetFormat", "value", "enableSplit", "onChange", "onSplitToggle", "splitFileCount", "min", "max", "onSplitCountChange", "_createCommentVNode", "type", "disabled", "startProcess", "loading", "isProcessing", "cancelProcess", "progress", "percentage", "overallProgressPercentage", "status", "isCompleted", "_toDisplayString", "currentOperation", "fileProgresses", "length", "_Fragment", "_renderList", "fileProgress", "index", "fileName", "progressPercentage", "size", "getStatusName", "defineComponent", "name", "setup", "ref", "sourceFilePath", "DataLogFormat", "Blf", "currentTaskId", "progressTimer", "async", "response", "appApi", "dataLogConvert", "result", "data", "startProgressPolling", "error", "console", "window", "setInterval", "getProgress", "stopProgressPolling", "clearInterval", "ProcessStatus", "Pending", "Processing", "Completed", "Failed", "Cancelled", "onUnmounted", "__exports__"], "sourceRoot": ""}