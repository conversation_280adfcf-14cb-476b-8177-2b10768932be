{"version": 3, "file": "js/log-converter.8835a743.js", "mappings": "0LAEA,MAAMA,EAAa,CCDZC,MAAM,iBDEPC,EAAa,CCGVD,MAAM,qBDFTE,EAAa,CCyCRF,MAAM,kBDxCXG,EAAa,CACjBC,IAAK,ECkDoBJ,MAAM,oBD/C3BK,EAAa,CCmDRL,MAAM,qBDlDXM,EAAa,CACjBF,IAAK,ECoD2EJ,MAAM,iBDjDlFO,EAAa,CCmDJP,MAAM,sBDlDfQ,EAAa,CCoDAR,MAAM,aDnDnBS,EAAa,CCoDAT,MAAM,eDnDnBU,EAAc,CAClBN,IAAK,EC0DiCJ,MAAM,sBDvDxCW,EAAc,CCsEXX,MAAM,cDrETY,EAAc,CAAER,IAAK,GACrBS,EAAc,CAAET,IAAK,GACrBU,EAAc,CAClBV,IAAK,ECwEyBJ,MAAM,qBDpEhC,SAAUe,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAMC,GAAuBC,EAAAA,EAAAA,IAAkB,aACzCC,GAAsBD,EAAAA,EAAAA,IAAkB,YACxCE,GAA0BF,EAAAA,EAAAA,IAAkB,gBAC5CG,GAAsBH,EAAAA,EAAAA,IAAkB,YACxCI,GAA4BJ,EAAAA,EAAAA,IAAkB,kBAC9CK,GAAuBL,EAAAA,EAAAA,IAAkB,aACzCM,GAA6BN,EAAAA,EAAAA,IAAkB,mBAC/CO,GAAqBP,EAAAA,EAAAA,IAAkB,WACvCQ,GAAyBR,EAAAA,EAAAA,IAAkB,eAC3CS,GAAuBT,EAAAA,EAAAA,IAAkB,aAE/C,OAAQU,EAAAA,EAAAA,OCxCRC,EAAAA,EAAAA,IAoGM,MApGNnC,EAoGM,CD3DJkB,EAAO,MAAQA,EAAO,KCvCtBkB,EAAAA,EAAAA,IAAqC,OAAhCnC,MAAM,aAAY,YAAQ,KAG/BmC,EAAAA,EAAAA,IAkFM,MAlFNlC,EAkFM,EAhFJmC,EAAAA,EAAAA,IAkCUN,EAAA,CAlCAO,MAAOrB,EAAAsB,SAAU,cAAY,QAAQ,iBAAe,ODwC3D,CACDC,SAASC,EAAAA,EAAAA,ICxCT,IAQe,EARfJ,EAAAA,EAAAA,IAQeX,EAAA,CARDgB,MAAM,aAAW,CD0C3BF,SAASC,EAAAA,EAAAA,ICzCX,IAMW,EANXJ,EAAAA,EAAAA,IAMWZ,EAAA,CDqCLkB,WC3Ca1B,EAAAsB,SAASK,SD4CtB,sBAAuB1B,EAAO,KAAOA,EAAO,GAAM2B,GC5CrC5B,EAAAsB,SAASK,SAAQC,GAAEC,YAAY,iCAAkCC,OAAM9B,EAAA+B,aD+CnF,CC9CMC,QAAMR,EAAAA,EAAAA,IACf,IAEY,EAFZJ,EAAAA,EAAAA,IAEYd,EAAA,CAFA2B,QAAOjC,EAAAkC,YAAU,CDgDvBX,SAASC,EAAAA,EAAAA,IChDgB,IAE/BvB,EAAA,KAAAA,EAAA,KD+CQkC,EAAAA,EAAAA,ICjDuB,aDmDzBC,EAAG,EACHC,GAAI,CAAC,IACJ,EAAG,CAAC,cAETD,EAAG,GACF,EAAG,CAAC,aAAc,aAEvBA,EAAG,KCpDChB,EAAAA,EAAAA,IAKOX,EAAA,CALOgB,MAAM,SAAO,CDuD/BF,SAASC,EAAAA,EAAAA,ICtDX,IAGiB,EAHjBJ,EAAAA,EAAAA,IAGiBT,EAAA,CDqDXe,WCxDmB1B,EAAAsC,eAAeC,aDyDlC,sBAAuBtC,EAAO,KAAOA,EAAO,GAAM2B,GCzD/B5B,EAAAsC,eAAeC,aAAYX,ID0D7C,CACDL,SAASC,EAAAA,EAAAA,IC1Db,IAAmC,EAAnCJ,EAAAA,EAAAA,IAAmCV,EAAA,CAAxB8B,MAAO,GAAC,CD4DXjB,SAASC,EAAAA,EAAAA,IC5DI,IAAGvB,EAAA,KAAAA,EAAA,KD6DdkC,EAAAA,EAAAA,IC7DW,UD+DbC,EAAG,EACHC,GAAI,CAAC,MC/DbjB,EAAAA,EAAAA,IAAmCV,EAAA,CAAxB8B,MAAO,GAAC,CDkEXjB,SAASC,EAAAA,EAAAA,IClEI,IAAGvB,EAAA,KAAAA,EAAA,KDmEdkC,EAAAA,EAAAA,ICnEW,UDqEbC,EAAG,EACHC,GAAI,CAAC,OAGTD,EAAG,GACF,EAAG,CAAC,iBAETA,EAAG,KCxEPhB,EAAAA,EAAAA,IAQeX,EAAA,CARDgB,MAAM,UAAQ,CD2ExBF,SAASC,EAAAA,EAAAA,IC1EX,IAMW,EANXJ,EAAAA,EAAAA,IAMWZ,EAAA,CDsELkB,WC5Ea1B,EAAAsC,eAAeG,gBD6E5B,sBAAuBxC,EAAO,KAAOA,EAAO,GAAM2B,GC7ErC5B,EAAAsC,eAAeG,gBAAeb,GAAEC,YAAY,eD+ExD,CC9EMG,QAAMR,EAAAA,EAAAA,IACf,IAEY,EAFZJ,EAAAA,EAAAA,IAEYd,EAAA,CAFA2B,QAAOjC,EAAA0C,oBAAkB,CDgF/BnB,SAASC,EAAAA,EAAAA,IChFwB,IAEvCvB,EAAA,KAAAA,EAAA,KD+EQkC,EAAAA,EAAAA,ICjF+B,cDmFjCC,EAAG,EACHC,GAAI,CAAC,IACJ,EAAG,CAAC,cAETD,EAAG,GACF,EAAG,CAAC,iBAETA,EAAG,KCnFPhB,EAAAA,EAAAA,IAEeX,EAAA,CAFDgB,MAAM,SAAO,CDsFvBF,SAASC,EAAAA,EAAAA,ICrFX,IAA0E,EAA1EJ,EAAAA,EAAAA,IAA0ER,EAAA,CDuFpEc,WCvFc1B,EAAAsC,eAAeK,YDwF7B,sBAAuB1C,EAAO,KAAOA,EAAO,GAAM2B,GCxFpC5B,EAAAsC,eAAeK,YAAWf,GAAGgB,SAAQ5C,EAAA6C,eD0FlD,KAAM,EAAG,CAAC,aAAc,eAE7BT,EAAG,ICzFapC,EAAAsC,eAAeK,cD4F5B1B,EAAAA,EAAAA,OC5FP6B,EAAAA,EAAAA,IAEerC,EAAA,CD2FPrB,IAAK,EC7FmCqC,MAAM,UD+F7C,CACDF,SAASC,EAAAA,EAAAA,IC/Ff,IAA6F,EAA7FJ,EAAAA,EAAAA,IAA6FP,EAAA,CDiGnFa,WCjGgB1B,EAAA+C,eDkGhB,sBAAuB9C,EAAO,KAAOA,EAAO,GAAM2B,GClGlC5B,EAAA+C,eAAcnB,GAAGoB,IAAK,EAAIC,IAAK,IAAML,SAAQ5C,EAAAkD,oBDsG5D,KAAM,EAAG,CAAC,aAAc,eAE7Bd,EAAG,MAELe,EAAAA,EAAAA,IAAoB,IAAI,KAE9Bf,EAAG,GACF,EAAG,CAAC,WCxGPjB,EAAAA,EAAAA,IAQM,MARNjC,EAQM,EAPJkC,EAAAA,EAAAA,IAEYd,EAAA,CAFD8C,KAAK,UAAWC,UAAWrD,EAAAsD,QAAUrB,QAAOjC,EAAAuD,aAAeC,QAASxD,EAAAyD,cD8G5E,CACDlC,SAASC,EAAAA,EAAAA,IC/GkF,IAE7FvB,EAAA,KAAAA,EAAA,KD8GIkC,EAAAA,EAAAA,IChHyF,aDkH3FC,EAAG,EACHC,GAAI,CAAC,IACJ,EAAG,CAAC,WAAY,UAAW,aChH9BjB,EAAAA,EAAAA,IAEYd,EAAA,CAFD8C,KAAK,SAAUC,UAAWrD,EAAAyD,aAAexB,QAAOjC,EAAA0D,eDqHxD,CACDnC,SAASC,EAAAA,EAAAA,ICtH+D,IAE1EvB,EAAA,MAAAA,EAAA,MDqHIkC,EAAAA,EAAAA,ICvHsE,aDyHxEC,EAAG,EACHC,GAAI,CAAC,KACJ,EAAG,CAAC,WAAY,cCrHVrC,EAAA2D,WDwHN1C,EAAAA,EAAAA,OCxHLC,EAAAA,EAAAA,IAiBM,MAjBN/B,EAiBM,CDwGAc,EAAO,MAAQA,EAAO,KCxH1BkB,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRC,EAAAA,EAAAA,IAC0DL,EAAA,CAD5C6C,WAAY5D,EAAA2D,SAASE,0BAChCC,OAAQ9D,EAAA2D,SAASI,YAAc,UAAY,UD0HvC,KAAM,EAAG,CAAC,aAAc,YCzH/B5C,EAAAA,EAAAA,IAAgE,IAAhE9B,GAAgE2E,EAAAA,EAAAA,IAAhChE,EAAA2D,SAASM,kBAAgB,GAG9CjE,EAAA2D,SAASO,gBAAkBlE,EAAA2D,SAASO,eAAeC,OAAS,IDyH9DlD,EAAAA,EAAAA,OCzHTC,EAAAA,EAAAA,IASM,MATN5B,EASM,CDiHIW,EAAO,MAAQA,EAAO,KCzH9BkB,EAAAA,EAAAA,IAAe,UAAX,UAAM,KACVA,EAAAA,EAAAA,IAMM,MANN5B,EAMM,GDoHK0B,EAAAA,EAAAA,KAAW,ICzHpBC,EAAAA,EAAAA,IAIMkD,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAJ+BrE,EAAA2D,SAASO,eAAc,CAA/CI,EAAcC,MD0HTtD,EAAAA,EAAAA,OC1HlBC,EAAAA,EAAAA,IAIM,OAJyD9B,IAAKmF,EAAOvF,MAAM,sBD6HpE,EC5HXmC,EAAAA,EAAAA,IAAwD,MAAxD3B,GAAwDwE,EAAAA,EAAAA,IAA9BM,EAAaE,UAAQ,IAC/CrD,EAAAA,EAAAA,IAAuE,MAAvE1B,GAAuEuE,EAAAA,EAAAA,IAA3ChE,EAAAyE,cAAcH,EAAaR,SAAM,IAC7D1C,EAAAA,EAAAA,IAA6FL,EAAA,CAA/E6C,WAAYU,EAAaI,mBAAqB,aAAW,EAAOC,KAAK,SDiItE,KAAM,EAAG,CAAC,mBAEb,YAGRxB,EAAAA,EAAAA,IAAoB,IAAI,OAE9BA,EAAAA,EAAAA,IAAoB,IAAI,GCjIjBnD,EAAA2D,UAAUI,cDmIhB9C,EAAAA,EAAAA,OCnILC,EAAAA,EAAAA,IAWM,MAXNxB,EAWM,EAVJ0B,EAAAA,EAAAA,IASYJ,EAAA,CATD4D,KAAK,UAAUC,MAAM,OAAO,YAAU,aDuI1C,CCtIMC,OAAKtD,EAAAA,EAAAA,IACd,IAEY,EAFZJ,EAAAA,EAAAA,IAEYd,EAAA,CAFD8C,KAAK,UAAWnB,QAAOjC,EAAA+E,kBD0I3B,CACDxD,SAASC,EAAAA,EAAAA,IC3IqC,IAEpDvB,EAAA,MAAAA,EAAA,MD0IQkC,EAAAA,EAAAA,IC5I4C,gBD8I9CC,EAAG,EACHC,GAAI,CAAC,KACJ,EAAG,CAAC,aC7IXjB,EAAAA,EAAAA,IAEYd,EAAA,CAFA2B,QAAOjC,EAAAgF,cAAY,CD+IzBzD,SAASC,EAAAA,EAAAA,IC/IkB,IAEjCvB,EAAA,MAAAA,EAAA,MD8IQkC,EAAAA,EAAAA,IChJyB,aDkJ3BC,EAAG,EACHC,GAAI,CAAC,KACJ,EAAG,CAAC,cAETD,EAAG,QAGPe,EAAAA,EAAAA,IAAoB,IAAI,MChJ9BhC,EAAAA,EAAAA,IASM,MATNxB,EASM,CARQK,EAAAiF,WDmJPhE,EAAAA,EAAAA,OCnJLC,EAAAA,EAAAA,IAGO,OAAAtB,EAHe,SAChBoE,EAAAA,EAAAA,IAAGhE,EAAAiF,SAASC,QAAU,MAAQ,MAAO,WACrClB,EAAAA,EAAAA,IAAGhE,EAAAiF,SAASC,QAAUlF,EAAAmF,cAAcnF,EAAAiF,SAASG,QAAU,MAAJ,KDkJrDjC,EAAAA,EAAAA,IAAoB,IAAI,GChJfnD,EAAAiF,UDmJT9B,EAAAA,EAAAA,IAAoB,IAAI,KADvBlC,EAAAA,EAAAA,OClJLC,EAAAA,EAAAA,IAAwC,OAAArB,EAAjB,eACXG,EAAAyD,eDoJPxC,EAAAA,EAAAA,OCpJLC,EAAAA,EAAAA,IAEO,OAFPpB,EAAoD,YAC3CkE,EAAAA,EAAAA,IAAGhE,EAAA2D,UAAUE,2BAA6B,GAAI,KACvD,KDmJIV,EAAAA,EAAAA,IAAoB,IAAI,MAGlC,C,uBCpIA,GAAekC,EAAAA,EAAAA,IAAgB,CAC7BC,KAAM,mBACNC,KAAAA,GAEE,MAAMjE,GAAWkE,EAAAA,EAAAA,IAAI,CAAE7D,SAAU,KAC3BsD,GAAWO,EAAAA,EAAAA,IAA4B,MAGvClD,GAAiBkD,EAAAA,EAAAA,IAA2B,CAChDC,eAAgB,GAChBlD,aAAcmD,EAAAA,GAAcC,IAC5BlD,gBAAiB,GACjBE,aAAa,EACbI,eAAgB,EAChB6C,eAAgB,cAGZ7C,GAAiByC,EAAAA,EAAAA,IAAI,GACrBlC,GAAUkC,EAAAA,EAAAA,IAA2B,MACrCK,GAAgBL,EAAAA,EAAAA,IAAiC,MACjD7B,GAAW6B,EAAAA,EAAAA,IAA4B,MAGvC/B,GAAe+B,EAAAA,EAAAA,KAAI,GAEzB,IAAIM,EAA+B,KAGnC,MAAM5D,EAAa6D,UACjB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,eAAehE,aACvCiE,EAASH,EAASI,KAEpBD,EAAOE,SAAWF,EAAOxE,UAC3BL,EAASkB,MAAMb,SAAWwE,EAAOxE,eAC3BI,KACGoE,EAAOG,eAGhBC,QAAQC,MAAM,UAAWL,EAAOM,a,CAElC,MAAOD,GACPD,QAAQC,MAAM,UAAWA,E,GAIvBzE,EAAcgE,UAClB,GAAKzE,EAASkB,MAAMb,SAEpB,IACE,MAAMqE,QAAiBC,EAAAA,GAAOC,eAAenE,YAAYT,EAASkB,MAAMb,UACxEsD,EAASzC,MAAQwD,EAASI,KAC1B9D,EAAeE,MAAMiD,eAAiBnE,EAASkB,MAAMb,SAGjDsD,EAASzC,OAAO0C,eACZwB,G,CAER,MAAOF,GACPD,QAAQC,MAAM,UAAWA,E,GAIvB9D,EAAqBqD,UACzB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,eAAeS,eACvCR,EAASH,EAASI,KAEpBD,EAAOE,SAAWF,EAAOS,YAC3BtE,EAAeE,MAAMC,gBAAkB0D,EAAOS,WAG1C3B,EAASzC,OAAO0C,eACZwB,KAECP,EAAOG,eAGhBC,QAAQC,MAAM,WAAYL,EAAOM,a,CAEnC,MAAOD,GACPD,QAAQC,MAAM,WAAYA,E,GAIxBK,EAAmBA,KAEvBN,QAAQO,IAAI,WAGRC,EAAiBA,KAErBR,QAAQO,IAAI,UAIRJ,EAAcX,UAClB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,eAAec,eAAe1E,EAAeE,OAC3Ec,EAAQd,MAAQwD,EAASI,I,CACzB,MAAOI,GACPD,QAAQC,MAAM,QAASA,E,GAKrB3D,EAAgBA,KAChBP,EAAeE,MAAMG,cACvBI,EAAeP,MAAQ,EACvBF,EAAeE,MAAMO,eAAiB,GAEpCkC,EAASzC,OAAO0C,SAClBwB,KAKExD,EAAqBA,KACzBZ,EAAeE,MAAMO,eAAiBA,EAAeP,MACjDyC,EAASzC,OAAO0C,SAClBwB,KAIEnD,EAAewC,UACnBtC,EAAajB,OAAQ,EACrB,IACE,MAAMwD,QAAiBC,EAAAA,GAAOC,eAAe3C,aAAajB,EAAeE,OACzEqD,EAAcrD,MAAQwD,EAASI,KAG/Ba,G,CACA,MAAOT,GACPD,QAAQC,MAAM,UAAWA,GACzB/C,EAAajB,OAAQ,C,GAInByE,EAAuBA,KACtBpB,EAAcrD,OAAO0E,SAE1BpB,EAAgBqB,OAAOC,YAAYrB,UACjC,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,eAAemB,YAAYxB,EAAcrD,MAAO0E,QAC9EvD,EAASnB,MAAQwD,EAASI,KAEtBzC,EAASnB,OAAOuB,cAClBuD,IACA7D,EAAajB,OAAQ,E,CAEvB,MAAOgE,GACPD,QAAQC,MAAM,UAAWA,E,GAE1B,OAGCc,EAAsBA,KACtBxB,IACFyB,cAAczB,GACdA,EAAgB,OAIdpC,EAAgBqC,UACpB,GAAKF,EAAcrD,OAAO0E,OAE1B,UACQjB,EAAAA,GAAOC,eAAexC,cAAcmC,EAAcrD,MAAM0E,QAC9DI,IACA7D,EAAajB,OAAQ,C,CACrB,MAAOgE,GACPD,QAAQC,MAAM,UAAWA,E,GAIvBzB,EAAmBgB,UACvB,GAAKzD,EAAeE,MAAMC,gBAE1B,UACQwD,EAAAA,GAAOC,eAAesB,WAAWlF,EAAeE,MAAMC,gB,CAC5D,MAAO+D,GACPD,QAAQC,MAAM,WAAYA,E,GAIxBxB,EAAeA,KACnBC,EAASzC,MAAQ,KACjBc,EAAQd,MAAQ,KAChBqD,EAAcrD,MAAQ,KACtBmB,EAASnB,MAAQ,KACjBiB,EAAajB,OAAQ,EACrBO,EAAeP,MAAQ,EACvB8E,KAGIG,EAAkBC,IACtB,MAAMC,EAAQ,CAAC,QAAS,KAAM,KAAM,MACpC,GAAc,IAAVD,EAAa,MAAO,UACxB,MAAME,EAAIC,KAAKC,MAAMD,KAAKf,IAAIY,GAASG,KAAKf,IAAI,OAChD,OAAOe,KAAKE,MAAML,EAAQG,KAAKG,IAAI,KAAMJ,GAAK,KAAO,IAAM,IAAMD,EAAMC,IAGnEzC,EAAiBC,IACrB,OAAQA,GACN,KAAKM,EAAAA,GAAcC,IAAK,MAAO,MAC/B,KAAKD,EAAAA,GAAcuC,IAAK,MAAO,MAC/B,QAAS,MAAO,OAIdxD,EAAiBX,IACrB,OAAQA,GACN,KAAKoE,EAAAA,GAAcC,QAAS,MAAO,MACnC,KAAKD,EAAAA,GAAcE,WAAY,MAAO,MACtC,KAAKF,EAAAA,GAAcG,UAAW,MAAO,MACrC,KAAKH,EAAAA,GAAcI,OAAQ,MAAO,KAClC,KAAKJ,EAAAA,GAAcK,UAAW,MAAO,MACrC,QAAS,MAAO,OAQpB,OAJAC,EAAAA,EAAAA,IAAY,KACVlB,MAGK,CACLhG,WACA2D,WACA3C,iBACAS,iBACAO,UACAuC,gBACAlC,WACAF,eACAvB,aACAH,cACAW,qBACAmE,mBACAE,iBACAlE,gBACAK,qBACAK,eACAG,gBACAqB,mBACAC,eACAyC,iBACAtC,gBACAV,gBAEJ,I,UCvWF,MAAMgE,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS1I,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://tab-kit-web/./src/views/LogConverterView.vue?e8f7", "webpack://tab-kit-web/./src/views/LogConverterView.vue", "webpack://tab-kit-web/./src/views/LogConverterView.vue?5b3b"], "sourcesContent": ["import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"log-converter\" }\nconst _hoisted_2 = { class: \"converter-content\" }\nconst _hoisted_3 = { class: \"action-buttons\" }\nconst _hoisted_4 = {\n  key: 0,\n  class: \"progress-section\"\n}\nconst _hoisted_5 = { class: \"current-operation\" }\nconst _hoisted_6 = {\n  key: 0,\n  class: \"file-progress\"\n}\nconst _hoisted_7 = { class: \"file-progress-list\" }\nconst _hoisted_8 = { class: \"file-name\" }\nconst _hoisted_9 = { class: \"file-status\" }\nconst _hoisted_10 = {\n  key: 1,\n  class: \"completion-section\"\n}\nconst _hoisted_11 = { class: \"status-bar\" }\nconst _hoisted_12 = { key: 0 }\nconst _hoisted_13 = { key: 1 }\nconst _hoisted_14 = {\n  key: 2,\n  class: \"processing-status\"\n}\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_input = _resolveComponent(\"el-input\")!\n  const _component_el_form_item = _resolveComponent(\"el-form-item\")!\n  const _component_el_radio = _resolveComponent(\"el-radio\")!\n  const _component_el_radio_group = _resolveComponent(\"el-radio-group\")!\n  const _component_el_switch = _resolveComponent(\"el-switch\")!\n  const _component_el_input_number = _resolveComponent(\"el-input-number\")!\n  const _component_el_form = _resolveComponent(\"el-form\")!\n  const _component_el_progress = _resolveComponent(\"el-progress\")!\n  const _component_el_result = _resolveComponent(\"el-result\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _cache[15] || (_cache[15] = _createElementVNode(\"div\", { class: \"title-bar\" }, \"Log 转换工具\", -1)),\n    _createElementVNode(\"div\", _hoisted_2, [\n      _createVNode(_component_el_form, {\n        model: _ctx.fileForm,\n        \"label-width\": \"120px\",\n        \"label-position\": \"top\"\n      }, {\n        default: _withCtx(() => [\n          _createVNode(_component_el_form_item, { label: \"Log 文件路径:\" }, {\n            default: _withCtx(() => [\n              _createVNode(_component_el_input, {\n                modelValue: _ctx.fileForm.filePath,\n                \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((_ctx.fileForm.filePath) = $event)),\n                placeholder: \"请输入或选择一个 Log 文件，支持格式：.asc .blf\",\n                onBlur: _ctx.analyzeFile\n              }, {\n                append: _withCtx(() => [\n                  _createVNode(_component_el_button, { onClick: _ctx.selectFile }, {\n                    default: _withCtx(() => _cache[5] || (_cache[5] = [\n                      _createTextVNode(\" 选择文件 \")\n                    ])),\n                    _: 1,\n                    __: [5]\n                  }, 8, [\"onClick\"])\n                ]),\n                _: 1\n              }, 8, [\"modelValue\", \"onBlur\"])\n            ]),\n            _: 1\n          }),\n          _createVNode(_component_el_form_item, { label: \"目标格式:\" }, {\n            default: _withCtx(() => [\n              _createVNode(_component_el_radio_group, {\n                modelValue: _ctx.processRequest.targetFormat,\n                \"onUpdate:modelValue\": _cache[1] || (_cache[1] = ($event: any) => ((_ctx.processRequest.targetFormat) = $event))\n              }, {\n                default: _withCtx(() => [\n                  _createVNode(_component_el_radio, { value: 1 }, {\n                    default: _withCtx(() => _cache[6] || (_cache[6] = [\n                      _createTextVNode(\"ASC\")\n                    ])),\n                    _: 1,\n                    __: [6]\n                  }),\n                  _createVNode(_component_el_radio, { value: 2 }, {\n                    default: _withCtx(() => _cache[7] || (_cache[7] = [\n                      _createTextVNode(\"BLF\")\n                    ])),\n                    _: 1,\n                    __: [7]\n                  })\n                ]),\n                _: 1\n              }, 8, [\"modelValue\"])\n            ]),\n            _: 1\n          }),\n          _createVNode(_component_el_form_item, { label: \"输出文件夹:\" }, {\n            default: _withCtx(() => [\n              _createVNode(_component_el_input, {\n                modelValue: _ctx.processRequest.outputDirectory,\n                \"onUpdate:modelValue\": _cache[2] || (_cache[2] = ($event: any) => ((_ctx.processRequest.outputDirectory) = $event)),\n                placeholder: \"默认与源文件相同文件夹\"\n              }, {\n                append: _withCtx(() => [\n                  _createVNode(_component_el_button, { onClick: _ctx.selectOutputFolder }, {\n                    default: _withCtx(() => _cache[8] || (_cache[8] = [\n                      _createTextVNode(\" 选择文件夹 \")\n                    ])),\n                    _: 1,\n                    __: [8]\n                  }, 8, [\"onClick\"])\n                ]),\n                _: 1\n              }, 8, [\"modelValue\"])\n            ]),\n            _: 1\n          }),\n          _createVNode(_component_el_form_item, { label: \"启用分割:\" }, {\n            default: _withCtx(() => [\n              _createVNode(_component_el_switch, {\n                modelValue: _ctx.processRequest.enableSplit,\n                \"onUpdate:modelValue\": _cache[3] || (_cache[3] = ($event: any) => ((_ctx.processRequest.enableSplit) = $event)),\n                onChange: _ctx.onSplitToggle\n              }, null, 8, [\"modelValue\", \"onChange\"])\n            ]),\n            _: 1\n          }),\n          (_ctx.processRequest.enableSplit)\n            ? (_openBlock(), _createBlock(_component_el_form_item, {\n                key: 0,\n                label: \"分割文件数:\"\n              }, {\n                default: _withCtx(() => [\n                  _createVNode(_component_el_input_number, {\n                    modelValue: _ctx.splitFileCount,\n                    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = ($event: any) => ((_ctx.splitFileCount) = $event)),\n                    min: 1,\n                    max: 100,\n                    onChange: _ctx.onSplitCountChange\n                  }, null, 8, [\"modelValue\", \"onChange\"])\n                ]),\n                _: 1\n              }))\n            : _createCommentVNode(\"\", true)\n        ]),\n        _: 1\n      }, 8, [\"model\"]),\n      _createElementVNode(\"div\", _hoisted_3, [\n        _createVNode(_component_el_button, {\n          type: \"primary\",\n          disabled: !_ctx.preview,\n          onClick: _ctx.startProcess,\n          loading: _ctx.isProcessing\n        }, {\n          default: _withCtx(() => _cache[9] || (_cache[9] = [\n            _createTextVNode(\" 开始转换 \")\n          ])),\n          _: 1,\n          __: [9]\n        }, 8, [\"disabled\", \"onClick\", \"loading\"]),\n        _createVNode(_component_el_button, {\n          type: \"danger\",\n          disabled: !_ctx.isProcessing,\n          onClick: _ctx.cancelProcess\n        }, {\n          default: _withCtx(() => _cache[10] || (_cache[10] = [\n            _createTextVNode(\" 取消转换 \")\n          ])),\n          _: 1,\n          __: [10]\n        }, 8, [\"disabled\", \"onClick\"])\n      ]),\n      (_ctx.progress)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [\n            _cache[12] || (_cache[12] = _createElementVNode(\"h4\", null, \"转换进度\", -1)),\n            _createVNode(_component_el_progress, {\n              percentage: _ctx.progress.overallProgressPercentage,\n              status: _ctx.progress.isCompleted ? 'success' : 'active'\n            }, null, 8, [\"percentage\", \"status\"]),\n            _createElementVNode(\"p\", _hoisted_5, _toDisplayString(_ctx.progress.currentOperation), 1),\n            (_ctx.progress.fileProgresses && _ctx.progress.fileProgresses.length > 0)\n              ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [\n                  _cache[11] || (_cache[11] = _createElementVNode(\"h5\", null, \"文件处理详情\", -1)),\n                  _createElementVNode(\"div\", _hoisted_7, [\n                    (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.progress.fileProgresses, (fileProgress, index) => {\n                      return (_openBlock(), _createElementBlock(\"div\", {\n                        key: index,\n                        class: \"file-progress-item\"\n                      }, [\n                        _createElementVNode(\"div\", _hoisted_8, _toDisplayString(fileProgress.fileName), 1),\n                        _createElementVNode(\"div\", _hoisted_9, _toDisplayString(_ctx.getStatusName(fileProgress.status)), 1),\n                        _createVNode(_component_el_progress, {\n                          percentage: fileProgress.progressPercentage,\n                          \"show-text\": false,\n                          size: \"small\"\n                        }, null, 8, [\"percentage\"])\n                      ]))\n                    }), 128))\n                  ])\n                ]))\n              : _createCommentVNode(\"\", true)\n          ]))\n        : _createCommentVNode(\"\", true),\n      (_ctx.progress?.isCompleted)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [\n            _createVNode(_component_el_result, {\n              icon: \"success\",\n              title: \"转换完成\",\n              \"sub-title\": \"所有文件已成功转换\"\n            }, {\n              extra: _withCtx(() => [\n                _createVNode(_component_el_button, {\n                  type: \"primary\",\n                  onClick: _ctx.openOutputFolder\n                }, {\n                  default: _withCtx(() => _cache[13] || (_cache[13] = [\n                    _createTextVNode(\" 打开输出文件夹 \")\n                  ])),\n                  _: 1,\n                  __: [13]\n                }, 8, [\"onClick\"]),\n                _createVNode(_component_el_button, { onClick: _ctx.resetProcess }, {\n                  default: _withCtx(() => _cache[14] || (_cache[14] = [\n                    _createTextVNode(\" 重新开始 \")\n                  ])),\n                  _: 1,\n                  __: [14]\n                }, 8, [\"onClick\"])\n              ]),\n              _: 1\n            })\n          ]))\n        : _createCommentVNode(\"\", true)\n    ]),\n    _createElementVNode(\"div\", _hoisted_11, [\n      (_ctx.fileInfo)\n        ? (_openBlock(), _createElementBlock(\"span\", _hoisted_12, \" 文件: \" + _toDisplayString(_ctx.fileInfo.isValid ? '已选择' : '无效') + \" | 格式: \" + _toDisplayString(_ctx.fileInfo.isValid ? _ctx.getFormatName(_ctx.fileInfo.format) : '未知'), 1))\n        : _createCommentVNode(\"\", true),\n      (!_ctx.fileInfo)\n        ? (_openBlock(), _createElementBlock(\"span\", _hoisted_13, \"请选择 Log 文件\"))\n        : _createCommentVNode(\"\", true),\n      (_ctx.isProcessing)\n        ? (_openBlock(), _createElementBlock(\"span\", _hoisted_14, \" 转换中... \" + _toDisplayString(_ctx.progress?.overallProgressPercentage || 0) + \"% \", 1))\n        : _createCommentVNode(\"\", true)\n    ])\n  ]))\n}", "<template>\n  <div class=\"log-converter\">\n    <!-- 标题栏 -->\n    <div class=\"title-bar\">Log 转换工具</div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"converter-content\">\n      <!-- 1. 文件选择区域 -->\n      <el-form :model=\"fileForm\" label-width=\"120px\" label-position=\"top\">\n        <el-form-item label=\"Log 文件路径:\">\n          <el-input v-model=\"fileForm.filePath\" placeholder=\"请输入或选择一个 Log 文件，支持格式：.asc .blf\" @blur=\"analyzeFile\">\n            <template #append>\n              <el-button @click=\"selectFile\">\n                选择文件\n              </el-button>\n            </template>\n          </el-input>\n        </el-form-item>\n                <el-form-item label=\"目标格式:\">\n          <el-radio-group v-model=\"processRequest.targetFormat\">\n            <el-radio :value=\"1\">ASC</el-radio>\n            <el-radio :value=\"2\">BLF</el-radio>\n          </el-radio-group>\n        </el-form-item>\n\n        <el-form-item label=\"输出文件夹:\">\n          <el-input v-model=\"processRequest.outputDirectory\" placeholder=\"默认与源文件相同文件夹\">\n            <template #append>\n              <el-button @click=\"selectOutputFolder\">\n                选择文件夹\n              </el-button>\n            </template>\n          </el-input>\n        </el-form-item>\n\n        <el-form-item label=\"启用分割:\">\n          <el-switch v-model=\"processRequest.enableSplit\" @change=\"onSplitToggle\" />\n        </el-form-item>\n\n        <el-form-item v-if=\"processRequest.enableSplit\" label=\"分割文件数:\">\n          <el-input-number v-model=\"splitFileCount\" :min=\"1\" :max=\"100\" @change=\"onSplitCountChange\" />\n        </el-form-item>\n      </el-form>\n\n      <!-- 4. 操作控制区域 -->\n      <div class=\"action-buttons\">\n        <el-button type=\"primary\" :disabled=\"!preview\" @click=\"startProcess\" :loading=\"isProcessing\">\n          开始转换\n        </el-button>\n\n        <el-button type=\"danger\" :disabled=\"!isProcessing\" @click=\"cancelProcess\">\n          取消转换\n        </el-button>\n      </div>\n\n      <!-- 进度显示 -->\n      <div v-if=\"progress\" class=\"progress-section\">\n        <h4>转换进度</h4>\n        <el-progress :percentage=\"progress.overallProgressPercentage\"\n          :status=\"progress.isCompleted ? 'success' : 'active'\" />\n        <p class=\"current-operation\">{{ progress.currentOperation }}</p>\n\n        <!-- 文件处理进度 -->\n        <div v-if=\"progress.fileProgresses && progress.fileProgresses.length > 0\" class=\"file-progress\">\n          <h5>文件处理详情</h5>\n          <div class=\"file-progress-list\">\n            <div v-for=\"(fileProgress, index) in progress.fileProgresses\" :key=\"index\" class=\"file-progress-item\">\n              <div class=\"file-name\">{{ fileProgress.fileName }}</div>\n              <div class=\"file-status\">{{ getStatusName(fileProgress.status) }}</div>\n              <el-progress :percentage=\"fileProgress.progressPercentage\" :show-text=\"false\" size=\"small\" />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 完成状态 -->\n      <div v-if=\"progress?.isCompleted\" class=\"completion-section\">\n        <el-result icon=\"success\" title=\"转换完成\" sub-title=\"所有文件已成功转换\">\n          <template #extra>\n            <el-button type=\"primary\" @click=\"openOutputFolder\">\n              打开输出文件夹\n            </el-button>\n            <el-button @click=\"resetProcess\">\n              重新开始\n            </el-button>\n          </template>\n        </el-result>\n      </div>\n    </div>\n\n    <!-- 状态栏 -->\n    <div class=\"status-bar\">\n      <span v-if=\"fileInfo\">\n        文件: {{ fileInfo.isValid ? '已选择' : '无效' }} |\n        格式: {{ fileInfo.isValid ? getFormatName(fileInfo.format) : '未知' }}\n      </span>\n      <span v-if=\"!fileInfo\">请选择 Log 文件</span>\n      <span v-if=\"isProcessing\" class=\"processing-status\">\n        转换中... {{ progress?.overallProgressPercentage || 0 }}%\n      </span>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, ref, onUnmounted } from \"vue\";\nimport {\n  appApi,\n  DataLogFileInfo,\n  DataLogProcessRequest,\n  ProcessPreview,\n  DataLogProcessResult,\n  ProcessProgress,\n  DataLogFormat,\n  ProcessStatus\n} from \"@/api/appApi\";\n\nexport default defineComponent({\n  name: \"LogConverterView\",\n  setup() {\n    // 文件相关\n    const fileForm = ref({ filePath: '' });\n    const fileInfo = ref<DataLogFileInfo | null>(null);\n\n    // 处理请求\n    const processRequest = ref<DataLogProcessRequest>({\n      sourceFilePath: '',\n      targetFormat: DataLogFormat.Asc,\n      outputDirectory: '',\n      enableSplit: false,\n      splitFileCount: 1,\n      fileNamePrefix: 'converted'\n    });\n\n    const splitFileCount = ref(1);\n    const preview = ref<ProcessPreview | null>(null);\n    const processResult = ref<DataLogProcessResult | null>(null);\n    const progress = ref<ProcessProgress | null>(null);\n\n    // 状态控制\n    const isProcessing = ref(false);\n\n    let progressTimer: number | null = null;\n\n    // 文件选择和分析\n    const selectFile = async () => {\n      try {\n        const response = await appApi.dataLogConvert.selectFile();\n        const result = response.data;\n\n        if (result.success && result.filePath) {\n          fileForm.value.filePath = result.filePath;\n          await analyzeFile();\n        } else if (result.userCancelled) {\n          // 用户取消选择，不做任何操作\n        } else {\n          console.error('文件选择失败:', result.errorMessage);\n        }\n      } catch (error) {\n        console.error('文件选择失败:', error);\n      }\n    };\n\n    const analyzeFile = async () => {\n      if (!fileForm.value.filePath) return;\n\n      try {\n        const response = await appApi.dataLogConvert.analyzeFile(fileForm.value.filePath);\n        fileInfo.value = response.data;\n        processRequest.value.sourceFilePath = fileForm.value.filePath;\n\n        // 自动预览\n        if (fileInfo.value?.isValid) {\n          await autoPreview();\n        }\n      } catch (error) {\n        console.error('文件分析失败:', error);\n      }\n    };\n\n    const selectOutputFolder = async () => {\n      try {\n        const response = await appApi.dataLogConvert.selectFolder();\n        const result = response.data;\n\n        if (result.success && result.folderPath) {\n          processRequest.value.outputDirectory = result.folderPath;\n\n          // 重新预览\n          if (fileInfo.value?.isValid) {\n            await autoPreview();\n          }\n        } else if (result.userCancelled) {\n          // 用户取消选择，不做任何操作\n        } else {\n          console.error('文件夹选择失败:', result.errorMessage);\n        }\n      } catch (error) {\n        console.error('文件夹选择失败:', error);\n      }\n    };\n\n    const openSourceFolder = () => {\n      // TODO: 实现打开源文件夹\n      console.log('打开源文件夹');\n    };\n\n    const openSourceFile = () => {\n      // TODO: 实现打开源文件\n      console.log('打开源文件');\n    };\n\n    // 自动预览\n    const autoPreview = async () => {\n      try {\n        const response = await appApi.dataLogConvert.previewProcess(processRequest.value);\n        preview.value = response.data;\n      } catch (error) {\n        console.error('预览失败:', error);\n      }\n    };\n\n    // 分割开关变化\n    const onSplitToggle = () => {\n      if (processRequest.value.enableSplit) {\n        splitFileCount.value = 1;\n        processRequest.value.splitFileCount = 1;\n      }\n      if (fileInfo.value?.isValid) {\n        autoPreview();\n      }\n    };\n\n    // 分割文件数变化\n    const onSplitCountChange = () => {\n      processRequest.value.splitFileCount = splitFileCount.value;\n      if (fileInfo.value?.isValid) {\n        autoPreview();\n      }\n    };\n\n    const startProcess = async () => {\n      isProcessing.value = true;\n      try {\n        const response = await appApi.dataLogConvert.startProcess(processRequest.value);\n        processResult.value = response.data;\n\n        // 开始轮询进度\n        startProgressPolling();\n      } catch (error) {\n        console.error('开始处理失败:', error);\n        isProcessing.value = false;\n      }\n    };\n\n    const startProgressPolling = () => {\n      if (!processResult.value?.taskId) return;\n\n      progressTimer = window.setInterval(async () => {\n        try {\n          const response = await appApi.dataLogConvert.getProgress(processResult.value!.taskId);\n          progress.value = response.data;\n\n          if (progress.value?.isCompleted) {\n            stopProgressPolling();\n            isProcessing.value = false;\n          }\n        } catch (error) {\n          console.error('获取进度失败:', error);\n        }\n      }, 1000);\n    };\n\n    const stopProgressPolling = () => {\n      if (progressTimer) {\n        clearInterval(progressTimer);\n        progressTimer = null;\n      }\n    };\n\n    const cancelProcess = async () => {\n      if (!processResult.value?.taskId) return;\n\n      try {\n        await appApi.dataLogConvert.cancelProcess(processResult.value.taskId);\n        stopProgressPolling();\n        isProcessing.value = false;\n      } catch (error) {\n        console.error('取消处理失败:', error);\n      }\n    };\n\n    const openOutputFolder = async () => {\n      if (!processRequest.value.outputDirectory) return;\n\n      try {\n        await appApi.dataLogConvert.openFolder(processRequest.value.outputDirectory);\n      } catch (error) {\n        console.error('打开文件夹失败:', error);\n      }\n    };\n\n    const resetProcess = () => {\n      fileInfo.value = null;\n      preview.value = null;\n      processResult.value = null;\n      progress.value = null;\n      isProcessing.value = false;\n      splitFileCount.value = 1;\n      stopProgressPolling();\n    };\n\n    const formatFileSize = (bytes: number): string => {\n      const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n      if (bytes === 0) return '0 Bytes';\n      const i = Math.floor(Math.log(bytes) / Math.log(1024));\n      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n    };\n\n    const getFormatName = (format: DataLogFormat): string => {\n      switch (format) {\n        case DataLogFormat.Asc: return 'ASC';\n        case DataLogFormat.Blf: return 'BLF';\n        default: return '未知';\n      }\n    };\n\n    const getStatusName = (status: ProcessStatus): string => {\n      switch (status) {\n        case ProcessStatus.Pending: return '等待中';\n        case ProcessStatus.Processing: return '处理中';\n        case ProcessStatus.Completed: return '已完成';\n        case ProcessStatus.Failed: return '失败';\n        case ProcessStatus.Cancelled: return '已取消';\n        default: return '未知';\n      }\n    };\n\n    onUnmounted(() => {\n      stopProgressPolling();\n    });\n\n    return {\n      fileForm,\n      fileInfo,\n      processRequest,\n      splitFileCount,\n      preview,\n      processResult,\n      progress,\n      isProcessing,\n      selectFile,\n      analyzeFile,\n      selectOutputFolder,\n      openSourceFolder,\n      openSourceFile,\n      onSplitToggle,\n      onSplitCountChange,\n      startProcess,\n      cancelProcess,\n      openOutputFolder,\n      resetProcess,\n      formatFileSize,\n      getFormatName,\n      getStatusName\n    };\n  },\n});\n</script>\n\n<style scoped>\n.log-converter {\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  overflow: hidden;\n}\n\n.title-bar {\n  font-size: 16px;\n  margin: 0 20px;\n  padding: 10px;\n  color: var(--el-text-color-primary);\n  font-weight: bold;\n  border-bottom: solid var(--el-color-primary) 2.5px;\n  flex-shrink: 0;\n}\n\n.converter-content {\n  flex: 1;\n  padding: 20px;\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  margin: 0 0 0 10px;\n  width: 100%;\n}\n\n.file-info {\n  background-color: #f8f9fa;\n  margin: 0;\n  border-radius: 0;\n}\n\n.file-info h4 {\n  color: #2c3e50;\n  margin-bottom: 15px;\n  font-size: 1rem;\n}\n\n.info-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 10px;\n  margin-bottom: 15px;\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n}\n\n.info-item .label {\n  color: #7f8c8d;\n  font-weight: 500;\n}\n\n.info-item .value {\n  color: #2c3e50;\n  font-weight: bold;\n}\n\n.file-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.error-info {\n  margin-top: 15px;\n}\n\n.preview-placeholder {\n  text-align: center;\n  padding: 40px 20px;\n  color: #7f8c8d;\n}\n\n.placeholder-icon {\n  font-size: 3rem;\n  margin-bottom: 15px;\n  opacity: 0.5;\n}\n\n.preview-content {\n  padding: 10px 0;\n}\n\n.preview-summary {\n  background-color: #f8f9fa;\n  padding: 15px;\n  border-radius: 6px;\n  margin-bottom: 20px;\n}\n\n.summary-item {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 8px;\n}\n\n.summary-item:last-child {\n  margin-bottom: 0;\n}\n\n.summary-item .label {\n  color: #7f8c8d;\n  font-weight: 500;\n}\n\n.summary-item .value {\n  color: #2c3e50;\n  font-weight: bold;\n}\n\n.output-files h4 {\n  color: #2c3e50;\n  margin-bottom: 15px;\n  font-size: 1rem;\n}\n\n.file-list {\n  max-height: 200px;\n  overflow-y: auto;\n}\n\n.file-item {\n  display: flex;\n  align-items: center;\n  padding: 10px;\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n  margin-bottom: 8px;\n  background-color: #fff;\n}\n\n.file-item:last-child {\n  margin-bottom: 0;\n}\n\n.file-name {\n  flex: 1;\n  color: #2c3e50;\n  font-weight: 500;\n}\n\n.file-size {\n  color: #7f8c8d;\n  margin-right: 10px;\n  font-size: 0.9rem;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 10px;\n  margin-bottom: 20px;\n}\n\n.progress-section {\n  margin-top: 20px;\n  padding-top: 20px;\n  border-top: 1px solid #e9ecef;\n}\n\n.progress-section h4 {\n  color: #2c3e50;\n  margin-bottom: 15px;\n  font-size: 1rem;\n}\n\n.current-operation {\n  color: #7f8c8d;\n  margin-top: 10px;\n  font-size: 0.9rem;\n}\n\n.file-progress {\n  margin-top: 20px;\n}\n\n.file-progress h5 {\n  color: #2c3e50;\n  margin-bottom: 10px;\n  font-size: 0.9rem;\n}\n\n.file-progress-list {\n  max-height: 150px;\n  overflow-y: auto;\n}\n\n.file-progress-item {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  padding: 8px;\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n  margin-bottom: 6px;\n  background-color: #fff;\n}\n\n.file-progress-item:last-child {\n  margin-bottom: 0;\n}\n\n.file-progress-item .file-name {\n  flex: 1;\n  font-size: 0.9rem;\n}\n\n.file-progress-item .file-status {\n  width: 80px;\n  font-size: 0.8rem;\n  color: #7f8c8d;\n}\n\n.completion-section {\n  margin-top: 20px;\n  padding-top: 20px;\n  border-top: 1px solid var(--el-border-color-light);\n}\n\n.status-bar {\n  background-color: var(--el-fill-color-light);\n  padding: 10px 20px;\n  border-top: 1px solid var(--el-border-color-base);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 0.9rem;\n  color: var(--el-text-color-regular);\n  flex-shrink: 0;\n}\n\n.processing-status {\n  color: var(--el-color-primary);\n  font-weight: bold;\n}\n\n@media (max-width: 768px) {\n  .converter-content {\n    padding: 10px;\n  }\n\n  .action-buttons {\n    flex-direction: column;\n  }\n\n  .file-item,\n  .file-progress-item {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 5px;\n  }\n\n  .status-bar {\n    flex-direction: column;\n    gap: 5px;\n    text-align: center;\n  }\n}\n</style>\n", "import { render } from \"./LogConverterView.vue?vue&type=template&id=43360292&scoped=true&ts=true\"\nimport script from \"./LogConverterView.vue?vue&type=script&lang=ts\"\nexport * from \"./LogConverterView.vue?vue&type=script&lang=ts\"\n\nimport \"./LogConverterView.vue?vue&type=style&index=0&id=43360292&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-43360292\"]])\n\nexport default __exports__"], "names": ["_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "key", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "_component_el_button", "_resolveComponent", "_component_el_input", "_component_el_form_item", "_component_el_radio", "_component_el_radio_group", "_component_el_switch", "_component_el_input_number", "_component_el_form", "_component_el_progress", "_component_el_result", "_openBlock", "_createElementBlock", "_createElementVNode", "_createVNode", "model", "fileForm", "default", "_withCtx", "label", "modelValue", "filePath", "$event", "placeholder", "onBlur", "analyzeFile", "append", "onClick", "selectFile", "_createTextVNode", "_", "__", "processRequest", "targetFormat", "value", "outputDirectory", "selectOutputFolder", "enableSplit", "onChange", "onSplitToggle", "_createBlock", "splitFileCount", "min", "max", "onSplitCountChange", "_createCommentVNode", "type", "disabled", "preview", "startProcess", "loading", "isProcessing", "cancelProcess", "progress", "percentage", "overallProgressPercentage", "status", "isCompleted", "_toDisplayString", "currentOperation", "fileProgresses", "length", "_Fragment", "_renderList", "fileProgress", "index", "fileName", "getStatusName", "progressPercentage", "size", "icon", "title", "extra", "openOutputFolder", "resetProcess", "fileInfo", "<PERSON><PERSON><PERSON><PERSON>", "getFormatName", "format", "defineComponent", "name", "setup", "ref", "sourceFilePath", "DataLogFormat", "Asc", "fileNamePrefix", "processResult", "progressTimer", "async", "response", "appApi", "dataLogConvert", "result", "data", "success", "userCancelled", "console", "error", "errorMessage", "autoPreview", "selectFolder", "folderPath", "openSourceFolder", "log", "openSourceFile", "previewProcess", "startProgressPolling", "taskId", "window", "setInterval", "getProgress", "stopProgressPolling", "clearInterval", "openFolder", "formatFileSize", "bytes", "sizes", "i", "Math", "floor", "round", "pow", "Blf", "ProcessStatus", "Pending", "Processing", "Completed", "Failed", "Cancelled", "onUnmounted", "__exports__"], "sourceRoot": ""}