import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";

import { appApi, type ErrorData } from './api/appApi' // 导入 appApi 而不是 axios
import { setupErrorHandler } from './utils/errorHandler' // 导入错误处理器

// 引入 Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

// 设置 Element Plus 主题变量
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import './styles/element-variables.css' // 需要创建这个文件来自定义主题

// 引入 FontAwesome
import { library } from '@fortawesome/fontawesome-svg-core'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import {
  faCogs, faFolderOpen, faPlus, faFileAlt,
  faHistory, faTrashCan, faFileExcel, faClock,
  faFolder, faChartBar, faProjectDiagram,
  faClockRotateLeft, faFileCircleExclamation,
  faAngleDown, faAngleUp, faExpand, faCompress,
  faUpRightAndDownLeftFromCenter, faDownLeftAndUpRightToCenter,
  faHome, faExchangeAlt, faInfoCircle, faChevronLeft, faChevronRight,
  faEye, faPlay, faStop, faRefresh, faSearch, faDownload, faTrash,
  faCode, faEnvelope, faGlobe
} from '@fortawesome/free-solid-svg-icons'

import { faGithub } from '@fortawesome/free-brands-svg-icons'

// 添加需要使用的图标到库中
library.add(
  faCogs, faFolderOpen, faPlus, faFileAlt,
  faHistory, faTrashCan, faFileExcel, faClock,
  faFolder, faChartBar, faProjectDiagram,
  faClockRotateLeft, faFileCircleExclamation,
  faAngleDown, faAngleUp, faExpand, faCompress,
  faUpRightAndDownLeftFromCenter, faDownLeftAndUpRightToCenter,
  faHome, faExchangeAlt, faInfoCircle, faChevronLeft, faChevronRight,
  faEye, faPlay, faStop, faRefresh, faSearch, faDownload, faTrash,
  faCode, faEnvelope, faGlobe, faGithub
)

const app = createApp(App)

// 全局注册 FontAwesome 组件
app.component('font-awesome-icon', FontAwesomeIcon)

// 全局注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 设置全局错误处理
setupErrorHandler()

app.use(store)
   .use(router)
   .use(ElementPlus, {
     locale: zhCn,
     size: 'default'
   })
   .mount('#app')

// 定义 sendError 类型
type SendErrorType = Error | unknown;

// 全局异常处理
app.config.errorHandler = (err: unknown, vm, info) => {
  // 控制台输出错误
  console.error("Vue 全局错误:", err);

  // 将错误发送到后端
  const errorData: ErrorData = {
    message: err instanceof Error ? err.message : String(err),
    stack: err instanceof Error ? err.stack : "无堆栈信息",
    vueHookInfo: info, // 更新字段名
    url: window.location.href,
  };

  appApi.logError(errorData).catch((sendError: SendErrorType) => {
    console.error("发送错误到服务器失败:", sendError);
  });
};

// 捕获未处理的Promise异常
window.addEventListener("unhandledrejection", (event) => {
  const errorData: ErrorData = {
    message:
      event.reason instanceof Error
        ? event.reason.message
        : "未处理的Promise异常",
    stack: event.reason instanceof Error ? event.reason.stack : "无堆栈信息",
    url: window.location.href,
    type: "unhandledrejection",
  };

  appApi.logError(errorData).catch((sendError: SendErrorType) => {
    console.error("发送Promise错误到服务器失败:", sendError);
  });
});

// 捕获全局JS错误
window.addEventListener("error", (event) => {
  // 过滤资源加载错误
  if (event.message) {
    const errorData: ErrorData = {
      message: event.message,
      codeInfo: `${event.filename}:${event.lineno}:${event.colno}`,
      url: window.location.href,
      type: "global-error",
    };

    appApi.logError(errorData).catch((sendError: SendErrorType) => {
      console.error("发送全局错误到服务器失败:", sendError);
    });
  }
});
