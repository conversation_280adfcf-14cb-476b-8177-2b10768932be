<template>
  <div class="log-converter">
    <!-- 标题栏 -->
    <div class="title-bar">Log 转换工具</div>

    <!-- 主要内容区域 -->
    <div class="converter-content">
      <div class="config-row">
        <div class="config-item" style="width:60%;">
          <label class="config-label" style="margin-bottom: 10px;">Log 文件路径:</label>
          <el-input v-model="fileForm.filePath" placeholder="请选择一个 Log 文件或输入路径，支持格式：.asc .blf" @blur="analyzeFile">
            <template #append>
              <el-button @click="selectFile">
                选择 Log 文件
              </el-button>
            </template>
          </el-input>
        </div>
      </div>

      <div class="config-row">
        <div class="config-item">
          <label class="config-label">目标格式</label>
          <el-radio-group v-model="processRequest.targetFormat">
            <el-radio :value="1">ASC</el-radio>
            <el-radio :value="2">BLF</el-radio>
          </el-radio-group>
        </div>

        <div class="config-item">
          <label class="config-label">启用分割</label>
          <el-switch v-model="processRequest.enableSplit" @change="onSplitToggle" />
        </div>

        <div class="config-item" v-if="processRequest.enableSplit">
          <label class="config-label">分割文件数</label>
          <el-input-number v-model="splitFileCount" :min="1" :max="100" @change="onSplitCountChange"
            class="split-count-input" />
        </div>
      </div>

      <!-- 操作控制区域 -->
      <div class="action-buttons">
        <el-button type="primary" :disabled="!fileForm.filePath" @click="startProcess" :loading="isProcessing">
          开始转换
        </el-button>

        <el-button type="danger" :disabled="!isProcessing" @click="cancelProcess">
          取消转换
        </el-button>
      </div>

      <!-- 进度显示 -->
      <div v-if="progress" class="progress-section">
        <h4>转换进度</h4>
        <el-progress :percentage="progress.overallProgressPercentage"
          :status="progress.isCompleted ? 'success' : 'active'" />
        <p class="current-operation">{{ progress.currentOperation }}</p>

        <!-- 文件处理进度 -->
        <div v-if="progress.fileProgresses && progress.fileProgresses.length > 0" class="file-progress">
          <div class="file-progress-list">
            <div v-for="(fileProgress, index) in progress.fileProgresses" :key="index" class="file-progress-item">
              <div class="file-name">{{ fileProgress.fileName }}</div>
              <el-progress :percentage="fileProgress.progressPercentage" :show-text="false" size="small"
                style="width: 100px;" />
              <div class="file-status">{{ getStatusName(fileProgress.status) }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 状态栏 -->
    <div class="status-bar">
      <span v-if="fileForm.filePath">
        文件: 已选择 - {{ fileForm.filePath }}
      </span>
      <span v-if="!fileForm.filePath">请选择 Log 文件</span>
      <span v-if="isProcessing" class="processing-status">
        转换中... {{ progress?.overallProgressPercentage || 0 }}%
      </span>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onUnmounted } from "vue";
import {
  appApi,
  DataLogProcessRequest,
  ProcessProgress,
  DataLogFormat,
  ProcessStatus
} from "@/api/appApi";

export default defineComponent({
  name: "LogConverterView",
  setup() {
    // 文件相关
    const fileForm = ref({ filePath: '' });

    // 处理请求
    const processRequest = ref<DataLogProcessRequest>({
      sourceFilePath: '',
      targetFormat: DataLogFormat.Blf,
      enableSplit: false,
      splitFileCount: 1
    });

    const splitFileCount = ref(1);
    const currentTaskId = ref<string | null>(null);
    const progress = ref<ProcessProgress | null>(null);

    // 状态控制
    const isProcessing = ref(false);

    let progressTimer: number | null = null;

    // 文件选择
    const selectFile = async () => {
      const response = await appApi.dataLogConvert.selectFile();
      const result = response.data;

      if (result) {
        fileForm.value.filePath = result;
        processRequest.value.sourceFilePath = result;
      }
    };

    const analyzeFile = async () => {
      if (!fileForm.value.filePath) return;
      processRequest.value.sourceFilePath = fileForm.value.filePath;
    };

    // 分割开关变化
    const onSplitToggle = () => {
      if (processRequest.value.enableSplit) {
        splitFileCount.value = 1;
        processRequest.value.splitFileCount = 1;
      }
    };

    // 分割文件数变化
    const onSplitCountChange = () => {
      processRequest.value.splitFileCount = splitFileCount.value;
    };

    const startProcess = async () => {
      isProcessing.value = true;
      try {
        const response = await appApi.dataLogConvert.startProcess(processRequest.value);
        currentTaskId.value = response.data;

        // 开始轮询进度
        startProgressPolling();
      } catch (error) {
        console.error('开始处理失败:', error);
        isProcessing.value = false;
      }
    };

    const startProgressPolling = () => {
      if (!currentTaskId.value) return;

      progressTimer = window.setInterval(async () => {
        try {
          const response = await appApi.dataLogConvert.getProgress(currentTaskId.value!);
          progress.value = response.data;

          if (progress.value?.isCompleted) {
            stopProgressPolling();
            isProcessing.value = false;
          }
        } catch (error) {
          console.error('获取进度失败:', error);
        }
      }, 1000);
    };

    const stopProgressPolling = () => {
      if (progressTimer) {
        clearInterval(progressTimer);
        progressTimer = null;
      }
    };

    const cancelProcess = async () => {
      if (!currentTaskId.value) return;

      try {
        await appApi.dataLogConvert.cancelProcess(currentTaskId.value);
        stopProgressPolling();
        isProcessing.value = false;
      } catch (error) {
        console.error('取消处理失败:', error);
      }
    };

    const getStatusName = (status: ProcessStatus): string => {
      switch (status) {
        case ProcessStatus.Pending: return '等待中';
        case ProcessStatus.Processing: return '处理中';
        case ProcessStatus.Completed: return '已完成';
        case ProcessStatus.Failed: return '失败';
        case ProcessStatus.Cancelled: return '已取消';
        default: return '未知';
      }
    };

    onUnmounted(() => {
      stopProgressPolling();
    });

    return {
      fileForm,
      processRequest,
      splitFileCount,
      progress,
      isProcessing,
      selectFile,
      analyzeFile,
      onSplitToggle,
      onSplitCountChange,
      startProcess,
      cancelProcess,
      getStatusName
    };
  },
});
</script>

<style scoped>
.log-converter {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.title-bar {
  font-size: 18px;
  margin: 0 20px;
  padding: 10px;
  color: var(--el-text-color-primary);
  font-weight: bold;
  border-bottom: solid var(--el-border-color-base) 1px;
  flex-shrink: 0;
  height: 60px;
  display: flex;
  align-items: center;
}

.converter-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin: 0 10px;
}

.file-info {
  background-color: #f8f9fa;
  margin: 0;
  border-radius: 0;
}

.file-info h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1rem;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
}

.info-item .label {
  color: #7f8c8d;
  font-weight: 500;
}

.info-item .value {
  color: #2c3e50;
  font-weight: bold;
}

.file-actions {
  display: flex;
  gap: 10px;
}

.error-info {
  margin-top: 15px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.progress-section {
  border-top: 1px solid #e9ecef;
  padding-top: 10px;
}

.progress-section h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1rem;
}

.current-operation {
  color: #7f8c8d;
  margin-top: 10px;
  font-size: 0.9rem;
}

.file-progress {
  margin-top: 20px;
}

.file-progress h5 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 0.9rem;
}

.file-progress-list {
  overflow-y: auto;
}

.file-progress-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  margin-bottom: 6px;
  background-color: #fff;
  max-width: 78vw;
}

.file-progress-item:last-child {
  margin-bottom: 0;
}

.file-progress-item .file-name {
  flex: 1;
  font-size: 0.9rem;
  color: #2c3e50;
  font-weight: 500;
}

.file-progress-item .file-status {
  width: 80px;
  font-size: 0.8rem;
  color: #7f8c8d;
}

.completion-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--el-border-color-light);
}

.status-bar {
  margin: 0 10px;
  height: 54px;
  background-color: var(--el-fill-color-light);
  padding: 10px 20px;
  border-top: 1px solid var(--el-border-color-base);
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  color: var(--el-text-color-regular);
  flex-shrink: 0;

  span {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 60vw;
  }
}

.processing-status {
  color: var(--el-color-primary);
  font-weight: bold;
}

.config-row {
  display: flex;
  align-items: flex-start;
  gap: 60px;
  flex-wrap: wrap;
}

.config-item {
  display: flex;
  flex-direction: column;
}

.config-label {
  font-size: 14px;
  color: var(--el-text-color-regular);
  font-weight: 500;
  margin-bottom: 4px;
}

.split-count-input {
  width: 120px;
}

@media (max-width: 768px) {
  .converter-content {
    padding: 10px;
  }

  .config-row {
    flex-direction: column;
    gap: 15px;
  }

  .config-item {
    min-width: auto;
    width: 100%;
  }

  .action-buttons {
    flex-direction: column;
  }

  .file-progress-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .status-bar {
    flex-direction: column;
    gap: 5px;
    text-align: center;
  }
}
</style>
