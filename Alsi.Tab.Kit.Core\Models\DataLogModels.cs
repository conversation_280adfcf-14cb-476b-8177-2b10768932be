using System;

namespace Alsi.Tab.Kit.Core.Models
{
    public enum DataLogFormat
    {
        Unknown = 0,
        Asc = 1,
        Blf = 2
    }

    public class DataLogProcessRequest
    {
        public string SourceFilePath { get; set; }
        public DataLogFormat TargetFormat { get; set; }
        public bool EnableSplit { get; set; }
        public int SplitFileCount { get; set; } = 1;
    }

    public class ProcessProgress
    {
        public Guid TaskId { get; set; }
        public int OverallProgressPercentage { get; set; }
        public string CurrentOperation { get; set; }
        public bool IsCompleted { get; set; }
        public string ErrorMessage { get; set; }
        public FileProgress[] FileProgresses { get; set; }
    }

    public class FileProgress
    {
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public ProcessStatus Status { get; set; }
        public int ProgressPercentage { get; set; }
        public string ErrorMessage { get; set; }
    }

    public enum ProcessStatus
    {
        Pending = 0,
        Processing = 1,
        Completed = 2,
        Failed = 3,
        Cancelled = 4
    }
}