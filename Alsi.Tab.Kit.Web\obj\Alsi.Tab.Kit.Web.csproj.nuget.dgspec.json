{"format": 1, "restore": {"D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\Alsi.Tab.Kit.Web.csproj": {}}, "projects": {"D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Desktop\\Alsi.App.Desktop.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Desktop\\Alsi.App.Desktop.csproj", "projectName": "Alsi.App.Desktop", "projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Desktop\\Alsi.App.Desktop.csproj", "packagesPath": "D:\\nuget_packages", "outputPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Desktop\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net462"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net462": {"projectReferences": {"D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\Alsi.App.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\Alsi.App.csproj"}}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net462": {"dependencies": {"MahApps.Metro.IconPacks.Material": {"target": "Package", "version": "[4.11.0, )"}, "Microsoft.AspNet.WebApi.Owin": {"target": "Package", "version": "[5.3.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Ookii.Dialogs.Wpf": {"target": "Package", "version": "[5.0.1, )"}, "System.Runtime.CompilerServices.Unsafe": {"target": "Package", "version": "[6.1.1, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\Alsi.App.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\Alsi.App.csproj", "projectName": "Alsi.App", "projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\Alsi.App.csproj", "packagesPath": "D:\\nuget_packages", "outputPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net462"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net462": {"projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net462": {"dependencies": {"Microsoft.AspNet.WebApi.OwinSelfHost": {"target": "Package", "version": "[5.3.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[4.1.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "SharpZipLib": {"target": "Package", "version": "[1.4.2, )"}, "System.Runtime.CompilerServices.Unsafe": {"target": "Package", "version": "[6.1.1, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Log\\Alsi.Common.Log.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Log\\Alsi.Common.Log.csproj", "projectName": "Alsi.Common.Log", "projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Log\\Alsi.Common.Log.csproj", "packagesPath": "D:\\nuget_packages", "outputPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Log\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net462"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net462": {"projectReferences": {"D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj"}}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net462": {"dependencies": {"Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Parsers\\Alsi.Common.Parsers.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Parsers\\Alsi.Common.Parsers.csproj", "projectName": "Alsi.Common.Parsers", "projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Parsers\\Alsi.Common.Parsers.csproj", "packagesPath": "D:\\nuget_packages", "outputPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Parsers\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net462"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net462": {"projectReferences": {"D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj"}}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net462": {"dependencies": {"NPOI": {"target": "Package", "version": "[2.6.2, )"}, "SharpZipLib": {"target": "Package", "version": "[1.4.2, )"}, "System.Runtime.CompilerServices.Unsafe": {"target": "Package", "version": "[6.1.1, )"}, "System.Threading.Tasks.Extensions": {"target": "Package", "version": "[4.5.4, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj", "projectName": "Alsi.Common.Utils", "projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj", "packagesPath": "D:\\nuget_packages", "outputPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net462", "netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net462": {"targetAlias": "net462", "projectReferences": {}}, "netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net462": {"targetAlias": "net462", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}, "netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj": {"restore": {"projectUniqueName": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj", "projectName": "Alsi.Tab.Kit.Core", "projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj", "UsingMicrosoftNETSdk": false, "frameworks": {"net462": {"projectReferences": {"D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Log\\Alsi.Common.Log.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Log\\Alsi.Common.Log.csproj"}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Parsers\\Alsi.Common.Parsers.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Parsers\\Alsi.Common.Parsers.csproj"}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj"}}}}}, "frameworks": {"net462": {}}}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\Alsi.Tab.Kit.Web.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\Alsi.Tab.Kit.Web.csproj", "projectName": "Alsi.Tab.Kit.Web", "projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\Alsi.Tab.Kit.Web.csproj", "packagesPath": "D:\\nuget_packages", "outputPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net462"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net462": {"projectReferences": {"D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Desktop\\Alsi.App.Desktop.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Desktop\\Alsi.App.Desktop.csproj"}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\Alsi.App.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\Alsi.App.csproj"}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Parsers\\Alsi.Common.Parsers.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Parsers\\Alsi.Common.Parsers.csproj"}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj"}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj"}}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net462": {"dependencies": {"AutoMapper": {"target": "Package", "version": "[8.1.1, )"}, "Microsoft.AspNet.WebApi.Owin": {"target": "Package", "version": "[5.3.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Runtime.CompilerServices.Unsafe": {"target": "Package", "version": "[6.1.1, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}}