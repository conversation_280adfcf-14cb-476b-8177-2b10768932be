{"version": 2, "dgSpecHash": "3pgyY5mRSEw=", "success": true, "projectFilePath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\Alsi.Tab.Kit.Web.csproj", "expectedPackageFiles": ["D:\\nuget_packages\\automapper\\8.1.1\\automapper.8.1.1.nupkg.sha512", "D:\\nuget_packages\\bouncycastle.cryptography\\2.2.1\\bouncycastle.cryptography.2.2.1.nupkg.sha512", "D:\\nuget_packages\\enums.net\\4.0.1\\enums.net.4.0.1.nupkg.sha512", "D:\\nuget_packages\\mahapps.metro.iconpacks.material\\4.11.0\\mahapps.metro.iconpacks.material.4.11.0.nupkg.sha512", "D:\\nuget_packages\\mathnet.numerics.signed\\4.15.0\\mathnet.numerics.signed.4.15.0.nupkg.sha512", "D:\\nuget_packages\\microsoft.aspnet.webapi.client\\6.0.0\\microsoft.aspnet.webapi.client.6.0.0.nupkg.sha512", "D:\\nuget_packages\\microsoft.aspnet.webapi.core\\5.3.0\\microsoft.aspnet.webapi.core.5.3.0.nupkg.sha512", "D:\\nuget_packages\\microsoft.aspnet.webapi.owin\\5.3.0\\microsoft.aspnet.webapi.owin.5.3.0.nupkg.sha512", "D:\\nuget_packages\\microsoft.aspnet.webapi.owinselfhost\\5.3.0\\microsoft.aspnet.webapi.owinselfhost.5.3.0.nupkg.sha512", "D:\\nuget_packages\\microsoft.io.recyclablememorystream\\2.3.2\\microsoft.io.recyclablememorystream.2.3.2.nupkg.sha512", "D:\\nuget_packages\\microsoft.owin\\4.2.2\\microsoft.owin.4.2.2.nupkg.sha512", "D:\\nuget_packages\\microsoft.owin.host.httplistener\\4.2.2\\microsoft.owin.host.httplistener.4.2.2.nupkg.sha512", "D:\\nuget_packages\\microsoft.owin.hosting\\4.2.2\\microsoft.owin.hosting.4.2.2.nupkg.sha512", "D:\\nuget_packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "D:\\nuget_packages\\newtonsoft.json.bson\\1.0.2\\newtonsoft.json.bson.1.0.2.nupkg.sha512", "D:\\nuget_packages\\npoi\\2.6.2\\npoi.2.6.2.nupkg.sha512", "D:\\nuget_packages\\ookii.dialogs.wpf\\5.0.1\\ookii.dialogs.wpf.5.0.1.nupkg.sha512", "D:\\nuget_packages\\owin\\1.0.0\\owin.1.0.0.nupkg.sha512", "D:\\nuget_packages\\serilog\\2.10.0\\serilog.2.10.0.nupkg.sha512", "D:\\nuget_packages\\serilog.sinks.console\\4.1.0\\serilog.sinks.console.4.1.0.nupkg.sha512", "D:\\nuget_packages\\serilog.sinks.file\\5.0.0\\serilog.sinks.file.5.0.0.nupkg.sha512", "D:\\nuget_packages\\sharpziplib\\1.4.2\\sharpziplib.1.4.2.nupkg.sha512", "D:\\nuget_packages\\sixlabors.fonts\\1.0.0\\sixlabors.fonts.1.0.0.nupkg.sha512", "D:\\nuget_packages\\sixlabors.imagesharp\\2.1.4\\sixlabors.imagesharp.2.1.4.nupkg.sha512", "D:\\nuget_packages\\system.buffers\\4.5.1\\system.buffers.4.5.1.nupkg.sha512", "D:\\nuget_packages\\system.configuration.configurationmanager\\6.0.0\\system.configuration.configurationmanager.6.0.0.nupkg.sha512", "D:\\nuget_packages\\system.memory\\4.5.5\\system.memory.4.5.5.nupkg.sha512", "D:\\nuget_packages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "D:\\nuget_packages\\system.runtime.compilerservices.unsafe\\6.1.1\\system.runtime.compilerservices.unsafe.6.1.1.nupkg.sha512", "D:\\nuget_packages\\system.security.accesscontrol\\6.0.0\\system.security.accesscontrol.6.0.0.nupkg.sha512", "D:\\nuget_packages\\system.security.cryptography.xml\\6.0.1\\system.security.cryptography.xml.6.0.1.nupkg.sha512", "D:\\nuget_packages\\system.security.permissions\\6.0.0\\system.security.permissions.6.0.0.nupkg.sha512", "D:\\nuget_packages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "D:\\nuget_packages\\system.text.encoding.codepages\\5.0.0\\system.text.encoding.codepages.5.0.0.nupkg.sha512", "D:\\nuget_packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512"], "logs": []}