0:00:38 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:00:38 Microsoft.Shutdown.OnWindowClosing_MaybeClearBrowsingDataOnExit
0:00:38 Microsoft.Shutdown.OnWindowClosing_ClearBrowsingDataOnExitNotInProgress
0:00:38 Microsoft.Shutdown.OnWindowClosingPostClearBrowsingData_NoBrowsingDataCleared
0:00:38 Microsoft.Shutdown.TabStripModel.CloseAllTabs
0:00:38 Microsoft.Shutdown.TabStripModel.CloseTabs
0:00:38 Microsoft.Shutdown.TabStripModel.CloseTabs_EdgeBeforeClosingAllTabs
0:00:38 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl
0:00:38 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_TabClosable
0:00:38 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_ClosingAll
0:00:38 Microsoft.Shutdown.TabStripModel.CloseWebContentses
0:00:38 Microsoft.Shutdown.TabStripModel.CloseWebContentses_FastShutdown
0:00:38 Microsoft.Shutdown.TabStripModel.ShouldRunUnloadListenerBeforeClosing_false
0:00:38 Microsoft.Shutdown.TabStripModel.RunUnloadListenerBeforeClosing_false
0:00:38 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_ClosedAll
0:00:38 Microsoft.Shutdown.TabStripModel.SendDetachWebContentsNotifications
0:00:38 Microsoft.UnloadController.ClearUnloadState
0:00:38 Browser2 Close Tab2 at 0
0:00:38 Tab2 WebContentsDestroyed
0:00:38 Microsoft.UnloadController.TabStripEmpty
0:00:38 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:00:38 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:00:38 Microsoft.Shutdown.OnWindowClosing
0:00:38 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:00:38 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:00:38 Microsoft.Shutdown.OnWindowClosingPostClearBrowsingData_NoBrowsingDataCleared
0:00:38 Widget Closed: BrowserFrame
0:00:38 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:00:38 Microsoft.Shutdown.OnWindowClosing
0:00:38 Microsoft.Browser_Removed
0:00:49 Widget Closed: StatusBubble
0:01:10 Microsoft.Shutdown.TabStripModel.SendDetachWebContentsNotifications
0:00:00 Startup
0:00:00 Microsoft.DeleteProfileHelper.CleanUpEphemeralProfiles
0:00:00 Microsoft.DeleteProfileHelper.CleanUpDeletedProfiles
0:00:00 Microsoft.NewBrowser_Popup
0:00:00 Microsoft.BrowserList.AddBrowser
0:00:00 Browser1 Insert active Tab1 at 0
0:00:00 Tab1 StartNav1 #auto_toplevel
0:00:00 Tab1 StartNav2 #typed
0:00:00 Tab1 FinishNav1
0:00:00 Tab1 PageLoad
0:00:00 Tab1 FinishNav2
0:00:00 Tab1 StartNav3 #renderer-script #link
0:00:00 Tab1 FinishNav3
0:00:00 Tab1 StartNav4 #renderer-script #link
0:00:00 Tab1 FinishNav4
0:00:00 Tab1 PageLoad
0:00:01 Tab1 StartNav5 #renderer-user #link
0:00:01 Tab1 FinishNav5
0:00:01 Tab1 StartNav6 #renderer-user #link
0:00:01 Tab1 FinishNav6
0:00:21 Microsoft.Shutdown.TabStripModel.SendDetachWebContentsNotifications
