﻿using Alsi.App;
using Alsi.App.Database;
using Alsi.App.Desktop;
using Alsi.App.Desktop.Utils;
using Alsi.App.Edge;
using Alsi.Tab.Kit.Views;
using Alsi.Tab.Kit.Web;
using System.ComponentModel;
using System.Windows;

namespace Alsi.Tab.Kit
{
    public partial class App : Application
    {
        private WebHostApp _webHostApp;

        private void Application_Startup(object sender, StartupEventArgs e)
        {
            var tabKitWebAssembly = new TabKitWebAssembly();
            _webHostApp = WebHostApp
                .Create(appName: "TabKit", appFolderName: "Alsi.Atts", productFolderName: "TabKit")
                .UseAppExceptionHandler(this)
                .UseSerilog("TabKit")
                .UseEdge()
                .UseFreeSql("TabKit.sqlite")
                .UseApiHost(tabKitWebAssembly)
                .Build();

            var mainWindow = WindowUtils.ShowWindow<MainView>("TabKit", null, size: new Size(1000, 600));
            mainWindow.MinWidth = 800;
            mainWindow.MinHeight = 600;

            Current.MainWindow = mainWindow;
            Current.MainWindow.Closing += MainWindow_Closing;
        }

        private void MainWindow_Closing(object sender, CancelEventArgs e)
        {
            e.Cancel = true;

            if (_webHostApp != null)
            {
                _webHostApp.Exit();
            }
        }
    }
}
