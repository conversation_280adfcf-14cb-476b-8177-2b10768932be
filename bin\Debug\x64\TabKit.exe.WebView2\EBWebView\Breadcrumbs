0:00:22 Tab1 StartNav6 #renderer-user #link
0:00:22 Tab1 FinishNav6
0:00:23 Tab1 StartNav7 #renderer-user #link
0:00:23 Tab1 FinishNav7
0:00:23 Tab1 StartNav8 #renderer-user #link
0:00:23 Tab1 FinishNav8
0:00:24 Tab1 StartNav9 #renderer-user #link
0:00:24 Tab1 FinishNav9
0:00:24 Tab1 StartNav10 #renderer-user #link
0:00:24 Tab1 FinishNav10
0:00:24 Tab1 StartNav11 #renderer-user #link
0:00:24 Tab1 FinishNav11
0:00:24 Tab1 StartNav12 #renderer-user #link
0:00:24 Tab1 FinishNav12
0:00:24 Tab1 StartNav13 #renderer-user #link
0:00:24 Tab1 FinishNav13
0:00:24 Tab1 StartNav14 #renderer-user #link
0:00:24 Tab1 FinishNav14
0:00:26 Tab1 StartNav15 #renderer-user #link
0:00:26 Tab1 FinishNav15
0:00:26 Tab1 StartNav16 #renderer-user #link
0:00:26 Tab1 FinishNav16
0:00:27 Tab1 StartNav17 #renderer-user #link
0:00:27 Tab1 FinishNav17
0:00:27 Tab1 StartNav18 #renderer-user #link
0:00:27 Tab1 FinishNav18
0:00:27 Tab1 StartNav19 #renderer-user #link
0:00:27 Tab1 FinishNav19
0:00:27 Tab1 StartNav20 #renderer-user #link
0:00:27 Tab1 FinishNav20
0:00:49 DevTools_ToggleWindow
0:00:49 DevTools_InspectRenderer
0:00:49 Microsoft.NewBrowser_DevTools
0:00:49 Microsoft.BrowserList.AddBrowser
0:00:49 Browser2 Insert active Tab2 at 0
0:00:49 Tab2 FinishNav21
0:00:49 Tab2 PageLoad
0:00:49 Microsoft.DevTools_NotECCohort
0:00:49 Microsoft.DevTools_NotECCohort
0:00:49 Microsoft.DevTools_NotECCohort
0:00:54 Microsoft.DevTools_NotECCohort
0:00:54 Microsoft.DevTools_NotECCohort
0:01:04 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:01:04 Microsoft.UnloadController.HasCompletedUnloadProcessing_False
0:01:04 Microsoft.UnloadController.HasCompletedUnloadProcessing_False
0:01:04 Tab2 BeforeUnload proceed:true
0:01:04 Microsoft.UnloadController.HasCompletedUnloadProcessing_False
0:01:04 Microsoft.UnloadController.ClearUnloadState
0:01:04 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:01:04 Microsoft.Shutdown.OnWindowClosing
0:01:04 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:01:04 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:01:04 Microsoft.Shutdown.OnWindowClosing_MaybeClearBrowsingDataOnExit
0:01:04 Microsoft.Shutdown.OnWindowClosing_ClearBrowsingDataOnExitNotInProgress
0:01:04 Microsoft.Shutdown.OnWindowClosingPostClearBrowsingData_NoBrowsingDataCleared
0:01:04 Microsoft.Shutdown.TabStripModel.CloseAllTabs
0:01:04 Microsoft.Shutdown.TabStripModel.CloseTabs
0:01:04 Microsoft.Shutdown.TabStripModel.CloseTabs_EdgeBeforeClosingAllTabs
0:01:04 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl
0:01:04 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_TabClosable
0:01:04 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_ClosingAll
0:01:04 Microsoft.Shutdown.TabStripModel.CloseWebContentses
0:01:04 Microsoft.Shutdown.TabStripModel.CloseWebContentses_FastShutdown
0:01:04 Microsoft.Shutdown.TabStripModel.ShouldRunUnloadListenerBeforeClosing_false
0:01:04 Microsoft.Shutdown.TabStripModel.RunUnloadListenerBeforeClosing_false
0:01:04 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_ClosedAll
0:01:04 Microsoft.Shutdown.TabStripModel.SendDetachWebContentsNotifications
0:01:04 Microsoft.UnloadController.ClearUnloadState
0:01:04 Browser2 Close Tab2 at 0
0:01:04 Tab2 WebContentsDestroyed
0:01:04 Microsoft.UnloadController.TabStripEmpty
0:01:04 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:01:04 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:01:04 Microsoft.Shutdown.OnWindowClosing
0:01:04 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:01:04 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:01:04 Microsoft.Shutdown.OnWindowClosingPostClearBrowsingData_NoBrowsingDataCleared
0:01:04 Widget Closed: BrowserFrame
0:01:04 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:01:04 Microsoft.Shutdown.OnWindowClosing
0:01:05 Microsoft.Browser_Removed
0:01:42 DevTools_ToggleWindow
0:01:42 DevTools_InspectRenderer
0:01:42 Microsoft.NewBrowser_DevTools
0:01:42 Microsoft.BrowserList.AddBrowser
0:01:42 Browser3 Insert active Tab3 at 0
0:01:42 Tab3 FinishNav22
0:01:42 Tab3 PageLoad
0:01:42 Microsoft.DevTools_NotECCohort
0:01:42 Microsoft.DevTools_NotECCohort
0:01:42 Microsoft.DevTools_NotECCohort
0:01:42 Tab3 StartNav23 #renderer-script #auto_subframe
0:01:43 Tab3 FinishNav23
0:01:43 Tab3 PageLoad
0:02:15 Microsoft.Clipboard.WriteText
0:03:03 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:00:00 Startup
0:00:00 Microsoft.DeleteProfileHelper.CleanUpEphemeralProfiles
0:00:00 Microsoft.DeleteProfileHelper.CleanUpDeletedProfiles
0:00:00 Microsoft.NewBrowser_Popup
0:00:00 Microsoft.BrowserList.AddBrowser
0:00:00 Browser1 Insert active Tab1 at 0
0:00:00 Tab1 StartNav1 #auto_toplevel
0:00:00 Tab1 StartNav2 #typed
0:00:00 Tab1 FinishNav1
0:00:00 Tab1 PageLoad
0:00:00 Tab1 FinishNav2
0:00:00 Tab1 StartNav3 #renderer-script #link
0:00:00 Tab1 FinishNav3
0:00:00 Tab1 StartNav4 #renderer-script #link
0:00:00 Tab1 FinishNav4
0:00:00 Tab1 PageLoad
0:00:01 Tab1 StartNav5 #renderer-user #link
0:00:01 Tab1 FinishNav5
0:00:01 Tab1 StartNav6 #renderer-user #link
0:00:01 Tab1 FinishNav6
0:00:02 Tab1 StartNav7 #renderer-user #link
0:00:02 Tab1 FinishNav7
0:00:02 Tab1 StartNav8 #renderer-user #link
0:00:02 Tab1 FinishNav8
0:00:11 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
