0:00:04 Tab1 FinishNav9
0:00:04 Tab1 StartNav10 #renderer-user #link
0:00:04 Tab1 FinishNav10
0:00:51 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:00:00 Startup
0:00:00 Microsoft.DeleteProfileHelper.CleanUpEphemeralProfiles
0:00:00 Microsoft.DeleteProfileHelper.CleanUpDeletedProfiles
0:00:00 Microsoft.NewBrowser_Popup
0:00:00 Microsoft.BrowserList.AddBrowser
0:00:00 Browser1 Insert active Tab1 at 0
0:00:00 Tab1 StartNav1 #auto_toplevel
0:00:00 Tab1 StartNav2 #typed
0:00:00 Tab1 FinishNav1
0:00:00 Tab1 PageLoad
0:00:00 Tab1 FinishNav2
0:00:00 Tab1 StartNav3 #renderer-script #link
0:00:00 Tab1 FinishNav3
0:00:00 Tab1 StartNav4 #renderer-script #link
0:00:00 Tab1 FinishNav4
0:00:00 Tab1 PageLoad
0:00:02 DevTools_ToggleWindow
0:00:02 DevTools_InspectRenderer
0:00:02 Microsoft.NewBrowser_DevTools
0:00:02 Microsoft.BrowserList.AddBrowser
0:00:02 Browser2 Insert active Tab2 at 0
0:00:02 Tab2 FinishNav5
0:00:03 Tab2 PageLoad
0:00:03 Microsoft.DevTools_NotECCohort
0:00:03 Microsoft.DevTools_NotECCohort
0:00:03 Microsoft.DevTools_NotECCohort
0:01:10 Tab1 StartNav6 #renderer-user #link
0:01:10 Tab1 FinishNav6
0:01:10 Tab1 StartNav7 #renderer-user #link
0:01:10 Tab1 FinishNav7
0:01:11 Tab1 StartNav8 #renderer-user #link
0:01:11 Tab1 FinishNav8
0:01:11 Tab1 StartNav9 #renderer-user #link
0:01:11 Tab1 FinishNav9
0:01:31 Microsoft.DevTools_NotECCohort
0:01:31 Microsoft.DevTools_NotECCohort
0:01:45 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
