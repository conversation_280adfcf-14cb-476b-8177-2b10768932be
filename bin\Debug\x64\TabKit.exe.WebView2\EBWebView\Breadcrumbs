0:00:00 Tab1 StartNav3 #renderer-script #link
0:00:00 Tab1 FinishNav3
0:00:00 Tab1 StartNav4 #renderer-script #link
0:00:00 Tab1 FinishNav4
0:00:00 Tab1 PageLoad
0:00:03 Tab1 StartNav5 #renderer-user #link
0:00:03 Tab1 FinishNav5
0:00:03 Tab1 StartNav6 #renderer-user #link
0:00:03 Tab1 FinishNav6
0:01:28 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:00:00 Startup
0:00:00 Microsoft.DeleteProfileHelper.CleanUpEphemeralProfiles
0:00:00 Microsoft.DeleteProfileHelper.CleanUpDeletedProfiles
0:00:00 Microsoft.NewBrowser_Popup
0:00:00 Microsoft.BrowserList.AddBrowser
0:00:00 Browser1 Insert active Tab1 at 0
0:00:00 Tab1 StartNav1 #auto_toplevel
0:00:00 Tab1 StartNav2 #typed
0:00:00 Tab1 FinishNav1
0:00:00 Tab1 PageLoad
0:00:00 Tab1 FinishNav2
0:00:00 Tab1 StartNav3 #renderer-script #link
0:00:00 Tab1 FinishNav3
0:00:00 Tab1 StartNav4 #renderer-script #link
0:00:00 Tab1 FinishNav4
0:00:00 Tab1 PageLoad
0:00:00 Tab1 StartNav5 #renderer-user #link
0:00:00 Tab1 FinishNav5
0:00:00 Tab1 StartNav6 #renderer-user #link
0:00:00 Tab1 FinishNav6
0:01:16 Microsoft.Clipboard.WriteText
0:01:16 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:01:16 Microsoft.UnloadController.HasCompletedUnloadProcessing_False
0:01:16 Microsoft.UnloadController.HasCompletedUnloadProcessing_False
0:01:16 Tab1 StartNav7 #renderer-user #link
0:01:16 Tab1 FinishNav7
0:01:16 Tab1 BeforeUnload proceed:true
0:01:16 Microsoft.UnloadController.HasCompletedUnloadProcessing_False
0:01:16 Microsoft.UnloadController.ClearUnloadState
0:01:16 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:01:16 Microsoft.Shutdown.OnWindowClosing
0:01:16 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:01:16 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:01:16 Microsoft.Shutdown.OnWindowClosing_MaybeClearBrowsingDataOnExit
0:01:16 Microsoft.Shutdown.OnWindowClosing_ClearBrowsingDataOnExitNotInProgress
0:01:16 Microsoft.Shutdown.OnWindowClosingPostClearBrowsingData_NoBrowsingDataCleared
0:01:16 Microsoft.Shutdown.TabStripModel.CloseAllTabs
0:01:16 Microsoft.Shutdown.TabStripModel.CloseTabs
0:01:16 Microsoft.Shutdown.TabStripModel.CloseTabs_EdgeBeforeClosingAllTabs
0:01:16 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl
0:01:16 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_TabClosable
0:01:16 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_ClosingAll
0:01:16 Microsoft.Shutdown.TabStripModel.CloseWebContentses
0:01:16 Microsoft.Shutdown.TabStripModel.CloseWebContentses_FastShutdown
0:01:16 Microsoft.Shutdown.TabStripModel.ShouldRunUnloadListenerBeforeClosing_false
0:01:16 Microsoft.Shutdown.TabStripModel.RunUnloadListenerBeforeClosing_false
0:01:16 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_ClosedAll
0:01:16 Microsoft.Shutdown.TabStripModel.SendDetachWebContentsNotifications
0:01:16 Microsoft.UnloadController.ClearUnloadState
0:01:16 Browser1 Close Tab1 at 0
0:01:16 Tab1 WebContentsDestroyed
0:01:16 Microsoft.UnloadController.TabStripEmpty
0:01:16 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:01:16 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:01:16 Microsoft.Shutdown.OnWindowClosing
0:01:16 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:01:16 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:01:16 Microsoft.Shutdown.OnWindowClosingPostClearBrowsingData_NoBrowsingDataCleared
0:01:16 Widget Closed: BrowserFrame
0:01:16 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:01:16 Microsoft.Shutdown.OnWindowClosing
0:01:16 Microsoft.Last_Browser_Removed
0:01:16 Microsoft.Shutdown.ShutdownIfNoBrowsers
0:01:16 Microsoft.Shutdown.SetTryingToQuit_Quitting
0:01:16 Microsoft.Shutdown.NotifyAppTerminating
0:01:16 Microsoft.Shutdown.OnAppExiting
0:01:16 Microsoft.Shutdown.HandleAppExitingForPlatform_Aura
0:01:16 Microsoft.Shutdown.HandleAppExitingForPlatform_Aura.NotificationUIManager_StartShutdown
0:01:16 Microsoft.Shutdown.HandleAppExitingForPlatform_Aura.CloseAllSecondaryWidgets
0:01:17 Shutdown
