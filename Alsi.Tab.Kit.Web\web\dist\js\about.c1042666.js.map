{"version": 3, "file": "js/about.c1042666.js", "mappings": "+KAEA,MAAMA,EAAa,CCDZC,MAAM,SDEPC,EAAa,CCAVD,MAAM,eDCTE,EAAa,CCARF,MAAM,YDCXG,EAAa,CCOVH,MAAM,YDNTI,EAAa,CCSJJ,MAAM,eDRfK,EAAa,CCwBJL,MAAM,eDvBfM,EAAa,CC8DJN,MAAM,eD7DfO,EAAa,CCmENP,MAAM,gBDjEb,SAAUQ,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAMC,GAA+BC,EAAAA,EAAAA,IAAkB,qBACjDC,GAAqBD,EAAAA,EAAAA,IAAkB,WAE7C,OAAQE,EAAAA,EAAAA,OCdRC,EAAAA,EAAAA,IAkFM,MAlFNpB,EAkFM,EAhFJqB,EAAAA,EAAAA,IAMM,MANNnB,EAMM,EALJmB,EAAAA,EAAAA,IAEM,MAFNlB,EAEM,EADJmB,EAAAA,EAAAA,IAAmDN,EAAA,CAAhCO,KAAK,OAAOtB,MAAM,gBDkBvCU,EAAO,KAAOA,EAAO,IChBrBU,EAAAA,EAAAA,IAAe,UAAX,UAAM,IDiBVV,EAAO,KAAOA,EAAO,IChBrBU,EAAAA,EAAAA,IAA+B,KAA5BpB,MAAM,WAAU,YAAQ,OAI7BoB,EAAAA,EAAAA,IAsEM,MAtENjB,EAsEM,EArEJkB,EAAAA,EAAAA,IAaUJ,EAAA,CAbDjB,MAAM,aAAW,CACbuB,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNJ,EAAAA,EAAAA,IAGM,MAHNhB,EAGM,EAFJiB,EAAAA,EAAAA,IAAwCN,EAAA,CAArBO,KAAK,gBDexBZ,EAAO,KAAOA,EAAO,ICdrBU,EAAAA,EAAAA,IAAiB,YAAX,QAAI,QDiBdK,SAASD,EAAAA,EAAAA,ICbT,IAIM,CDUJd,EAAO,KAAOA,EAAO,ICdvBU,EAAAA,EAAAA,IAIM,OAJDpB,MAAM,gBAAc,EACvBoB,EAAAA,EAAAA,IAEI,KAFDpB,MAAM,eAAc,sDDenB,MAEN0B,EAAG,EACHC,GAAI,CAAC,MCXPN,EAAAA,EAAAA,IAoCUJ,EAAA,CApCDjB,MAAM,aAAW,CACbuB,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNJ,EAAAA,EAAAA,IAGM,MAHNf,EAGM,EAFJgB,EAAAA,EAAAA,IAAiCN,EAAA,CAAdO,KAAK,SDcxBZ,EAAO,KAAOA,EAAO,ICbrBU,EAAAA,EAAAA,IAAiB,YAAX,QAAI,QDgBdK,SAASD,EAAAA,EAAAA,ICZT,IA2BM,CDdJd,EAAO,KAAOA,EAAO,ICbvBU,EAAAA,EAAAA,IA2BM,OA3BDpB,MAAM,aAAW,EACpBoB,EAAAA,EAAAA,IAyBM,OAzBDpB,MAAM,aAAW,EACpBoB,EAAAA,EAAAA,IAGM,OAHDpB,MAAM,aAAW,EACpBoB,EAAAA,EAAAA,IAAqC,QAA/BpB,MAAM,cAAa,UACzBoB,EAAAA,EAAAA,IAAkD,QAA5CpB,MAAM,cAAa,yBAE3BoB,EAAAA,EAAAA,IAGM,OAHDpB,MAAM,aAAW,EACpBoB,EAAAA,EAAAA,IAAsC,QAAhCpB,MAAM,cAAa,WACzBoB,EAAAA,EAAAA,IAA4C,QAAtCpB,MAAM,cAAa,mBAE3BoB,EAAAA,EAAAA,IAGM,OAHDpB,MAAM,aAAW,EACpBoB,EAAAA,EAAAA,IAAoC,QAA9BpB,MAAM,cAAa,SACzBoB,EAAAA,EAAAA,IAA2C,QAArCpB,MAAM,cAAa,kBAE3BoB,EAAAA,EAAAA,IAGM,OAHDpB,MAAM,aAAW,EACpBoB,EAAAA,EAAAA,IAAqC,QAA/BpB,MAAM,cAAa,UACzBoB,EAAAA,EAAAA,IAA8C,QAAxCpB,MAAM,cAAa,qBAE3BoB,EAAAA,EAAAA,IAGM,OAHDpB,MAAM,aAAW,EACpBoB,EAAAA,EAAAA,IAAqC,QAA/BpB,MAAM,cAAa,UACzBoB,EAAAA,EAAAA,IAA8C,QAAxCpB,MAAM,cAAa,qBAE3BoB,EAAAA,EAAAA,IAGM,OAHDpB,MAAM,aAAW,EACpBoB,EAAAA,EAAAA,IAAqC,QAA/BpB,MAAM,cAAa,UACzBoB,EAAAA,EAAAA,IAAiD,QAA3CpB,MAAM,cAAa,2BDgBzB,MAEN0B,EAAG,EACHC,GAAI,CAAC,MCZPN,EAAAA,EAAAA,IAaUJ,EAAA,CAbDjB,MAAM,aAAW,CACbuB,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNJ,EAAAA,EAAAA,IAGM,MAHNd,EAGM,EAFJe,EAAAA,EAAAA,IAAqCN,EAAA,CAAlBO,KAAK,aDexBZ,EAAO,KAAOA,EAAO,ICdrBU,EAAAA,EAAAA,IAAiB,YAAX,QAAI,QDiBdK,SAASD,EAAAA,EAAAA,ICbT,IAIM,EAJNJ,EAAAA,EAAAA,IAIM,MAJNb,EAIM,CDWFG,EAAO,KAAOA,EAAO,ICdvBU,EAAAA,EAAAA,IAAkC,YAA5B,yBAAqB,KAC3BC,EAAAA,EAAAA,IAA0DN,EAAA,CAAvCO,KAAK,WAAWtB,MAAM,iBDkBvCU,EAAO,KAAOA,EAAO,ICjBvBU,EAAAA,EAAAA,IAAuF,KAApFQ,KAAK,gCAAgC5B,MAAM,gBAAe,0BAAsB,QDuBrF0B,EAAG,OAIX,C,aChBA,GAAeG,EAAAA,EAAAA,IAAgB,CAC7BC,KAAM,YACNC,WAAY,CACVC,gBAAeA,EAAAA,M,UCtFnB,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASzB,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://tab-kit-web/./src/views/AboutView.vue?2c2c", "webpack://tab-kit-web/./src/views/AboutView.vue", "webpack://tab-kit-web/./src/views/AboutView.vue?d56f"], "sourcesContent": ["import { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, withCtx as _withCtx, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"about\" }\nconst _hoisted_2 = { class: \"page-header\" }\nconst _hoisted_3 = { class: \"app-logo\" }\nconst _hoisted_4 = { class: \"app-info\" }\nconst _hoisted_5 = { class: \"card-header\" }\nconst _hoisted_6 = { class: \"card-header\" }\nconst _hoisted_7 = { class: \"card-header\" }\nconst _hoisted_8 = { class: \"contact-item\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\")!\n  const _component_el_card = _resolveComponent(\"el-card\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _createElementVNode(\"div\", _hoisted_3, [\n        _createVNode(_component_font_awesome_icon, {\n          icon: \"cogs\",\n          class: \"logo-icon\"\n        })\n      ]),\n      _cache[0] || (_cache[0] = _createElementVNode(\"h1\", null, \"TabKit\", -1)),\n      _cache[1] || (_cache[1] = _createElementVNode(\"p\", { class: \"version\" }, \"版本 1.0.0\", -1))\n    ]),\n    _createElementVNode(\"div\", _hoisted_4, [\n      _createVNode(_component_el_card, { class: \"info-card\" }, {\n        header: _withCtx(() => [\n          _createElementVNode(\"div\", _hoisted_5, [\n            _createVNode(_component_font_awesome_icon, { icon: \"info-circle\" }),\n            _cache[2] || (_cache[2] = _createElementVNode(\"span\", null, \"应用信息\", -1))\n          ])\n        ]),\n        default: _withCtx(() => [\n          _cache[3] || (_cache[3] = _createElementVNode(\"div\", { class: \"info-content\" }, [\n            _createElementVNode(\"p\", { class: \"description\" }, \" Alsi.Tab.Kit 是一个集成多种工具的桌面应用程序，旨在提高工作效率和简化日常任务。 \")\n          ], -1))\n        ]),\n        _: 1,\n        __: [3]\n      }),\n      _createVNode(_component_el_card, { class: \"info-card\" }, {\n        header: _withCtx(() => [\n          _createElementVNode(\"div\", _hoisted_6, [\n            _createVNode(_component_font_awesome_icon, { icon: \"code\" }),\n            _cache[4] || (_cache[4] = _createElementVNode(\"span\", null, \"技术信息\", -1))\n          ])\n        ]),\n        default: _withCtx(() => [\n          _cache[5] || (_cache[5] = _createElementVNode(\"div\", { class: \"tech-info\" }, [\n            _createElementVNode(\"div\", { class: \"tech-grid\" }, [\n              _createElementVNode(\"div\", { class: \"tech-item\" }, [\n                _createElementVNode(\"span\", { class: \"tech-label\" }, \"前端框架:\"),\n                _createElementVNode(\"span\", { class: \"tech-value\" }, \"Vue 3 + TypeScript\")\n              ]),\n              _createElementVNode(\"div\", { class: \"tech-item\" }, [\n                _createElementVNode(\"span\", { class: \"tech-label\" }, \"UI组件库:\"),\n                _createElementVNode(\"span\", { class: \"tech-value\" }, \"Element Plus\")\n              ]),\n              _createElementVNode(\"div\", { class: \"tech-item\" }, [\n                _createElementVNode(\"span\", { class: \"tech-label\" }, \"图标库:\"),\n                _createElementVNode(\"span\", { class: \"tech-value\" }, \"FontAwesome\")\n              ]),\n              _createElementVNode(\"div\", { class: \"tech-item\" }, [\n                _createElementVNode(\"span\", { class: \"tech-label\" }, \"后端框架:\"),\n                _createElementVNode(\"span\", { class: \"tech-value\" }, \".NET Framework\")\n              ]),\n              _createElementVNode(\"div\", { class: \"tech-item\" }, [\n                _createElementVNode(\"span\", { class: \"tech-label\" }, \"桌面框架:\"),\n                _createElementVNode(\"span\", { class: \"tech-value\" }, \"WPF + WebView2\")\n              ]),\n              _createElementVNode(\"div\", { class: \"tech-item\" }, [\n                _createElementVNode(\"span\", { class: \"tech-label\" }, \"构建工具:\"),\n                _createElementVNode(\"span\", { class: \"tech-value\" }, \"Vue CLI + Webpack\")\n              ])\n            ])\n          ], -1))\n        ]),\n        _: 1,\n        __: [5]\n      }),\n      _createVNode(_component_el_card, { class: \"info-card\" }, {\n        header: _withCtx(() => [\n          _createElementVNode(\"div\", _hoisted_7, [\n            _createVNode(_component_font_awesome_icon, { icon: \"envelope\" }),\n            _cache[6] || (_cache[6] = _createElementVNode(\"span\", null, \"联系信息\", -1))\n          ])\n        ]),\n        default: _withCtx(() => [\n          _createElementVNode(\"div\", _hoisted_8, [\n            _cache[7] || (_cache[7] = _createElementVNode(\"span\", null, \"使用过程中遇到问题或有任何建议，欢迎联系：\", -1)),\n            _createVNode(_component_font_awesome_icon, {\n              icon: \"envelope\",\n              class: \"contact-icon\"\n            }),\n            _cache[8] || (_cache[8] = _createElementVNode(\"a\", {\n              href: \"mailto:<EMAIL>\",\n              class: \"contact-link\"\n            }, \"<EMAIL>\", -1))\n          ])\n        ]),\n        _: 1\n      })\n    ])\n  ]))\n}", "<template>\n  <div class=\"about\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <div class=\"app-logo\">\n        <font-awesome-icon icon=\"cogs\" class=\"logo-icon\" />\n      </div>\n      <h1>TabKit</h1>\n      <p class=\"version\">版本 1.0.0</p>\n    </div>\n\n    <!-- 应用信息 -->\n    <div class=\"app-info\">\n      <el-card class=\"info-card\">\n        <template #header>\n          <div class=\"card-header\">\n            <font-awesome-icon icon=\"info-circle\" />\n            <span>应用信息</span>\n          </div>\n        </template>\n\n        <div class=\"info-content\">\n          <p class=\"description\">\n            Alsi.Tab.Kit 是一个集成多种工具的桌面应用程序，旨在提高工作效率和简化日常任务。\n          </p>\n        </div>\n      </el-card>\n\n      <!-- 技术信息 -->\n      <el-card class=\"info-card\">\n        <template #header>\n          <div class=\"card-header\">\n            <font-awesome-icon icon=\"code\" />\n            <span>技术信息</span>\n          </div>\n        </template>\n\n        <div class=\"tech-info\">\n          <div class=\"tech-grid\">\n            <div class=\"tech-item\">\n              <span class=\"tech-label\">前端框架:</span>\n              <span class=\"tech-value\">Vue 3 + TypeScript</span>\n            </div>\n            <div class=\"tech-item\">\n              <span class=\"tech-label\">UI组件库:</span>\n              <span class=\"tech-value\">Element Plus</span>\n            </div>\n            <div class=\"tech-item\">\n              <span class=\"tech-label\">图标库:</span>\n              <span class=\"tech-value\">FontAwesome</span>\n            </div>\n            <div class=\"tech-item\">\n              <span class=\"tech-label\">后端框架:</span>\n              <span class=\"tech-value\">.NET Framework</span>\n            </div>\n            <div class=\"tech-item\">\n              <span class=\"tech-label\">桌面框架:</span>\n              <span class=\"tech-value\">WPF + WebView2</span>\n            </div>\n            <div class=\"tech-item\">\n              <span class=\"tech-label\">构建工具:</span>\n              <span class=\"tech-value\">Vue CLI + Webpack</span>\n            </div>\n          </div>\n        </div>\n      </el-card>\n\n      <!-- 联系信息 -->\n      <el-card class=\"info-card\">\n        <template #header>\n          <div class=\"card-header\">\n            <font-awesome-icon icon=\"envelope\" />\n            <span>联系信息</span>\n          </div>\n        </template>\n\n        <div class=\"contact-item\">\n          <span>使用过程中遇到问题或有任何建议，欢迎联系：</span>\n          <font-awesome-icon icon=\"envelope\" class=\"contact-icon\" />\n          <a href=\"mailto:<EMAIL>\" class=\"contact-link\"><EMAIL></a>\n        </div>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent } from \"vue\";\nimport { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';\n\nexport default defineComponent({\n  name: \"AboutView\",\n  components: {\n    FontAwesomeIcon,\n  },\n});\n</script>\n\n<style scoped>\n.about {\n  padding: 20px;\n  max-width: 1000px;\n  margin: 0 auto;\n}\n\n.page-header {\n  text-align: center;\n  margin-bottom: 40px;\n}\n\n.app-logo {\n  margin-bottom: 20px;\n}\n\n.logo-icon {\n  font-size: 4rem;\n  color: var(--el-color-primary);\n}\n\n.page-header h1 {\n  font-size: 2.5rem;\n  color: #2c3e50;\n  margin-bottom: 10px;\n}\n\n.version {\n  color: #7f8c8d;\n  font-size: 1.1rem;\n  margin: 0;\n}\n\n.app-info {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  margin-bottom: 40px;\n}\n\n.info-card {\n  border-radius: 8px;\n}\n\n.card-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: bold;\n  color: #2c3e50;\n  font-size: 1.1rem;\n}\n\n.info-content {\n  line-height: 1.6;\n}\n\n.description {\n  color: #555;\n  font-size: 1rem;\n  margin-bottom: 25px;\n}\n\n.features h3 {\n  color: #2c3e50;\n  margin-bottom: 15px;\n  font-size: 1.2rem;\n}\n\n.features ul {\n  list-style: none;\n  padding: 0;\n}\n\n.features li {\n  display: flex;\n  align-items: flex-start;\n  gap: 10px;\n  margin-bottom: 15px;\n  padding: 10px;\n  background-color: #f8f9fa;\n  border-radius: 6px;\n}\n\n.feature-icon {\n  color: #3498db;\n  margin-top: 2px;\n  flex-shrink: 0;\n}\n\n.features li strong {\n  color: #2c3e50;\n}\n\n.tech-info {\n  padding: 10px 0;\n}\n\n.tech-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 15px;\n}\n\n.tech-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px;\n  background-color: #f8f9fa;\n  border-radius: 6px;\n}\n\n.tech-label {\n  color: #7f8c8d;\n  font-weight: 500;\n}\n\n.tech-value {\n  color: #2c3e50;\n  font-weight: bold;\n}\n\n.contact-item {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  padding: 10px;\n  background-color: #f8f9fa;\n  border-radius: 6px;\n}\n\n.contact-icon {\n  color: var(--el-color-primary);\n  width: 16px;\n}\n\n.contact-link {\n  color: var(--el-color-primary);\n  text-decoration: none;\n  transition: color 0.3s ease;\n}\n\n.contact-link:hover {\n  color: var(--el-color-primary-dark-1);\n  text-decoration: underline;\n}\n\n\n\n@media (max-width: 768px) {\n  .about {\n    padding: 15px;\n  }\n\n  .page-header h1 {\n    font-size: 2rem;\n  }\n\n  .logo-icon {\n    font-size: 3rem;\n  }\n\n  .tech-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .tech-item {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 5px;\n  }\n\n  .features li {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n  }\n}\n</style>\n", "import { render } from \"./AboutView.vue?vue&type=template&id=cda3dc76&scoped=true&ts=true\"\nimport script from \"./AboutView.vue?vue&type=script&lang=ts\"\nexport * from \"./AboutView.vue?vue&type=script&lang=ts\"\n\nimport \"./AboutView.vue?vue&type=style&index=0&id=cda3dc76&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-cda3dc76\"]])\n\nexport default __exports__"], "names": ["_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "_component_font_awesome_icon", "_resolveComponent", "_component_el_card", "_openBlock", "_createElementBlock", "_createElementVNode", "_createVNode", "icon", "header", "_withCtx", "default", "_", "__", "href", "defineComponent", "name", "components", "FontAwesomeIcon", "__exports__"], "sourceRoot": ""}