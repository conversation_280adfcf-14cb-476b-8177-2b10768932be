"use strict";(self["webpackChunktab_kit_web"]=self["webpackChunktab_kit_web"]||[]).push([[594],{7315:function(a,s,c){c.r(s),c.d(s,{default:function(){return L}});var e=c(6768),l=c(3935);const t={class:"about"},n={class:"app-info-cards"},i={class:"card-header"},d={class:"info-content"},o={class:"card-header"};function k(a,s,c,k,v,p){const h=(0,e.g2)("font-awesome-icon"),u=(0,e.g2)("el-card");return(0,e.uX)(),(0,e.CE)("div",t,[s[6]||(s[6]=(0,e.Fv)('<div class="page-header" data-v-769f0fd8><div class="app-logo-section" data-v-769f0fd8><img src="'+l+'" alt="TabKit Logo" class="logo-icon" data-v-769f0fd8><div class="app-info" data-v-769f0fd8><h1 data-v-769f0fd8>TabKit</h1><p class="version" data-v-769f0fd8>版本 1.0.0</p></div></div></div>',1)),(0,e.Lk)("div",n,[(0,e.bF)(u,{class:"info-card"},{header:(0,e.k6)(()=>[(0,e.Lk)("div",i,[(0,e.bF)(h,{icon:"info-circle"}),s[0]||(s[0]=(0,e.Lk)("span",null,"关于 TabKit",-1))])]),default:(0,e.k6)(()=>[(0,e.Lk)("div",d,[s[3]||(s[3]=(0,e.Lk)("p",{class:"description"}," Alsi.Tab.Kit 是一个集成多种工具的桌面应用程序，旨在提高工作效率和简化日常任务。 ",-1)),(0,e.Lk)("div",null,[s[1]||(s[1]=(0,e.Lk)("span",null,"使用过程中遇到问题或有任何建议，欢迎联系：",-1)),(0,e.bF)(h,{icon:"envelope",class:"contact-icon"}),s[2]||(s[2]=(0,e.Lk)("a",{href:"mailto:<EMAIL>",class:"contact-link"},"<EMAIL>",-1))])])]),_:1}),(0,e.bF)(u,{class:"info-card"},{header:(0,e.k6)(()=>[(0,e.Lk)("div",o,[(0,e.bF)(h,{icon:"code"}),s[4]||(s[4]=(0,e.Lk)("span",null,"技术信息",-1))])]),default:(0,e.k6)(()=>[s[5]||(s[5]=(0,e.Lk)("div",{class:"tech-info"},[(0,e.Lk)("div",{class:"tech-grid"},[(0,e.Lk)("div",{class:"tech-item"},[(0,e.Lk)("span",{class:"tech-label"},"前端框架:"),(0,e.Lk)("span",{class:"tech-value"},"Vue 3 + TypeScript")]),(0,e.Lk)("div",{class:"tech-item"},[(0,e.Lk)("span",{class:"tech-label"},"UI组件库:"),(0,e.Lk)("span",{class:"tech-value"},"Element Plus")]),(0,e.Lk)("div",{class:"tech-item"},[(0,e.Lk)("span",{class:"tech-label"},"图标库:"),(0,e.Lk)("span",{class:"tech-value"},"FontAwesome")]),(0,e.Lk)("div",{class:"tech-item"},[(0,e.Lk)("span",{class:"tech-label"},"后端框架:"),(0,e.Lk)("span",{class:"tech-value"},".NET Framework")]),(0,e.Lk)("div",{class:"tech-item"},[(0,e.Lk)("span",{class:"tech-label"},"桌面框架:"),(0,e.Lk)("span",{class:"tech-value"},"WPF + WebView2")]),(0,e.Lk)("div",{class:"tech-item"},[(0,e.Lk)("span",{class:"tech-label"},"构建工具:"),(0,e.Lk)("span",{class:"tech-value"},"Vue CLI + Webpack")])])],-1))]),_:1,__:[5]})])])}var v=c(292),p=(0,e.pM)({name:"AboutView",components:{FontAwesomeIcon:v.gc}}),h=c(1241);const u=(0,h.A)(p,[["render",k],["__scopeId","data-v-769f0fd8"]]);var L=u}}]);
//# sourceMappingURL=about.e1e6dfac.js.map