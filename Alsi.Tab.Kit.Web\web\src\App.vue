<template>
  <div id="app">
    <!-- 侧边菜单 -->
    <div class="sidebar" :class="{ collapsed: isMenuCollapsed }">
      <!-- 菜单头 -->
      <div class="menu-header">
        <div class="logo-container" v-if="!isMenuCollapsed">
          <img src="@/assets/logo.svg" alt="Logo" class="logo" />
          <span class="app-name">TabKit</span>
        </div>
        <div class="logo-container-collapsed" v-else>
          <img src="@/assets/logo.svg" alt="Logo" class="logo-small" />
        </div>
        <div class="menu-toggle" @click="toggleMenu">
          <font-awesome-icon :icon="isMenuCollapsed ? 'chevron-right' : 'chevron-left'" />
        </div>
      </div>

      <!-- 菜单项 -->
      <div class="menu-items">
        <!-- 主页 -->
        <router-link to="/" class="menu-item" active-class="active">
          <font-awesome-icon icon="home" class="menu-icon" />
          <span v-if="!isMenuCollapsed" class="menu-text">主页</span>
        </router-link>

        <!-- Log转换工具 -->
        <router-link to="/log-converter" class="menu-item" active-class="active">
          <font-awesome-icon icon="exchange-alt" class="menu-icon" />
          <span v-if="!isMenuCollapsed" class="menu-text">Log 转换工具</span>
        </router-link>
      </div>

      <!-- 底部菜单项 -->
      <div class="menu-bottom">
        <!-- 关于 -->
        <router-link to="/about" class="menu-item" active-class="active">
          <font-awesome-icon icon="info-circle" class="menu-icon" />
          <span v-if="!isMenuCollapsed" class="menu-text">关于</span>
        </router-link>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content" :class="{ expanded: isMenuCollapsed }">
      <router-view />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

export default defineComponent({
  name: 'App',
  components: {
    FontAwesomeIcon,
  },
  setup() {
    const isMenuCollapsed = ref(false);

    const toggleMenu = () => {
      isMenuCollapsed.value = !isMenuCollapsed.value;
    };

    return {
      isMenuCollapsed,
      toggleMenu,
    };
  },
});
</script>

<style lang="scss">
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  height: 100vh;
  display: flex;
  width: 100vw;
}

.sidebar {
  width: 200px;
  background-color: white;
  color: #333;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  position: relative;
  border-right: 1px solid #e4e7ed;

  &.collapsed {
    width: 60px;
  }

  .menu-header {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    border-bottom: 1px solid #e4e7ed;
    background-color: white;

    .logo-container {
      display: flex;
      align-items: center;
      gap: 10px;

      .logo {
        width: 32px;
        height: 32px;
      }

      .app-name {
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }
    }

    .logo-container-collapsed {
      display: flex;
      justify-content: center;
      width: 100%;

      .logo-small {
        width: 28px;
        height: 28px;
      }
    }

    .menu-toggle {
      width: 24px;
      height: 24px;
      background-color: #f5f7fa;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 12px;
      color: #666;
      transition: all 0.3s ease;

      &:hover {
        background-color: #e4e7ed;
        color: #333;
      }
    }
  }

  .menu-items {
    flex: 1;
    padding-top: 10px;
  }

  .menu-bottom {
    padding-bottom: 20px;
  }

  .menu-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #666;
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    margin: 2px 8px;
    border-radius: 6px;

    &:hover {
      background-color: #f5f7fa;
      color: #333;
    }

    &.active {
      background-color: #e6f7ff;
      color: var(--el-color-primary);
      border-left-color: var(--el-color-primary);
    }

    .menu-icon {
      font-size: 16px;
      width: 20px;
      text-align: center;
    }

    .menu-text {
      margin-left: 12px;
      font-size: 14px;
      white-space: nowrap;
      overflow: hidden;
      font-weight: 500;
    }
  }

  &.collapsed .menu-item {
    justify-content: center;
    padding: 12px 10px;
    margin: 2px 4px;

    .menu-icon {
      margin: 0;
    }
  }
}

.main-content {
  flex: 1;
  background-color: var(--el-fill-color-base);
  overflow-y: auto;
  transition: margin-left 0.3s ease;

  &.expanded {
    margin-left: 0;
  }
}
</style>
