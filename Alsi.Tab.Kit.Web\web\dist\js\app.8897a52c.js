(function(){"use strict";var e={1021:function(e,n,t){t.d(n,{GQ:function(){return c},KK:function(){return r},Mo:function(){return o}});var o,r,s=t(4373);(function(e){e[e["Unknown"]=0]="Unknown",e[e["Asc"]=1]="Asc",e[e["Blf"]=2]="Blf"})(o||(o={})),function(e){e[e["Pending"]=0]="Pending",e[e["Processing"]=1]="Processing",e[e["Completed"]=2]="Completed",e[e["Failed"]=3]="Failed",e[e["Cancelled"]=4]="Cancelled"}(r||(r={}));const a="/api/app",i="/api/DataLogConvert",c={getAppInfo(){return s.A.get(`${a}/appInfo`)},logError:e=>s.A.post(`${a}/logError`,e),exit:()=>s.A.post(`${a}/exit`),getTestModel(){return s.A.get("api/test/model")},dataLogConvert:{selectFile(){return s.A.post(`${i}/select-file`)},previewProcess(e){return s.A.post(`${i}/preview`,e)},startProcess(e){return s.A.post(`${i}/start`,e)},getProgress(e){return s.A.get(`${i}/progress?taskId=${e}`)},cancelProcess(e){return s.A.post(`${i}/cancel`,null,{params:{taskId:e}})}}}},8579:function(e,n,t){var o=t(5130),r=t(6768),s=t(4232);const a={id:"app"},i={class:"menu-items"},c={key:0,class:"menu-text"},l={key:0,class:"menu-text"},u={class:"menu-bottom"},d={key:0,class:"menu-text"};function f(e,n,t,o,f,p){const g=(0,r.g2)("font-awesome-icon"),m=(0,r.g2)("router-link"),v=(0,r.g2)("router-view");return(0,r.uX)(),(0,r.CE)("div",a,[(0,r.Lk)("div",{class:(0,s.C4)(["sidebar",{collapsed:e.isMenuCollapsed}])},[(0,r.Lk)("div",{class:"menu-toggle",onClick:n[0]||(n[0]=(...n)=>e.toggleMenu&&e.toggleMenu(...n))},[(0,r.bF)(g,{icon:e.isMenuCollapsed?"chevron-right":"chevron-left"},null,8,["icon"])]),(0,r.Lk)("div",i,[(0,r.bF)(m,{to:"/",class:"menu-item","active-class":"active"},{default:(0,r.k6)(()=>[(0,r.bF)(g,{icon:"home",class:"menu-icon"}),e.isMenuCollapsed?(0,r.Q3)("",!0):((0,r.uX)(),(0,r.CE)("span",c,"主页"))]),_:1}),(0,r.bF)(m,{to:"/log-converter",class:"menu-item","active-class":"active"},{default:(0,r.k6)(()=>[(0,r.bF)(g,{icon:"exchange-alt",class:"menu-icon"}),e.isMenuCollapsed?(0,r.Q3)("",!0):((0,r.uX)(),(0,r.CE)("span",l,"Log 转换工具"))]),_:1})]),(0,r.Lk)("div",u,[(0,r.bF)(m,{to:"/about",class:"menu-item","active-class":"active"},{default:(0,r.k6)(()=>[(0,r.bF)(g,{icon:"info-circle",class:"menu-icon"}),e.isMenuCollapsed?(0,r.Q3)("",!0):((0,r.uX)(),(0,r.CE)("span",d,"关于"))]),_:1})])],2),(0,r.Lk)("div",{class:(0,s.C4)(["main-content",{expanded:e.isMenuCollapsed}])},[(0,r.bF)(v)],2)])}var p=t(144),g=t(292),m=(0,r.pM)({name:"App",components:{FontAwesomeIcon:g.gc},setup(){const e=(0,p.KR)(!1),n=()=>{e.value=!e.value};return{isMenuCollapsed:e,toggleMenu:n}}}),v=t(1241);const h=(0,v.A)(m,[["render",f]]);var b=h,k=t(1387);const w={class:"home"},y={class:"tools-grid"},C={class:"tool-icon"},_={class:"tool-features"},L={class:"tool-icon"},E={class:"tool-features"};function A(e,n,t,o,s,a){const i=(0,r.g2)("font-awesome-icon"),c=(0,r.g2)("el-tag"),l=(0,r.g2)("el-card");return(0,r.uX)(),(0,r.CE)("div",w,[n[9]||(n[9]=(0,r.Lk)("div",{class:"page-header"},[(0,r.Lk)("h1",null,"TabKit 工具集合"),(0,r.Lk)("p",null,"TabKit 中集成了多种实用工具")],-1)),(0,r.Lk)("div",y,[(0,r.bF)(l,{class:"tool-card",shadow:"hover",onClick:n[0]||(n[0]=n=>e.navigateToTool("/log-converter"))},{default:(0,r.k6)(()=>[(0,r.Lk)("div",C,[(0,r.bF)(i,{icon:"exchange-alt"})]),n[4]||(n[4]=(0,r.Lk)("h3",null,"Log 转换工具",-1)),n[5]||(n[5]=(0,r.Lk)("p",null,"支持多种日志格式之间的转换，包括 ASC、BLF 等格式。提供批量处理和文件分割功能。",-1)),(0,r.Lk)("div",_,[(0,r.bF)(c,{size:"small"},{default:(0,r.k6)(()=>n[1]||(n[1]=[(0,r.eW)("格式转换")])),_:1,__:[1]}),(0,r.bF)(c,{size:"small",type:"success"},{default:(0,r.k6)(()=>n[2]||(n[2]=[(0,r.eW)("批量处理")])),_:1,__:[2]}),(0,r.bF)(c,{size:"small",type:"info"},{default:(0,r.k6)(()=>n[3]||(n[3]=[(0,r.eW)("文件分割")])),_:1,__:[3]})])]),_:1,__:[4,5]}),(0,r.bF)(l,{class:"tool-card coming-soon",shadow:"hover"},{default:(0,r.k6)(()=>[(0,r.Lk)("div",L,[(0,r.bF)(i,{icon:"cogs"})]),n[7]||(n[7]=(0,r.Lk)("h3",null,"更多工具",-1)),n[8]||(n[8]=(0,r.Lk)("p",null,"更多实用工具正在开发中，敬请期待...",-1)),(0,r.Lk)("div",E,[(0,r.bF)(c,{size:"small",type:"info"},{default:(0,r.k6)(()=>n[6]||(n[6]=[(0,r.eW)("即将推出")])),_:1,__:[6]})])]),_:1,__:[7,8]})])])}t(4114);var F=(0,r.pM)({name:"HomeView",components:{FontAwesomeIcon:g.gc},setup(){const e=(0,k.rd)(),n=n=>{e.push(n)};return{navigateToTool:n}}});const j=(0,v.A)(F,[["render",A],["__scopeId","data-v-6213a60e"]]);var M=j;const x=[{path:"/",name:"home",component:M},{path:"/log-converter",name:"log-converter",component:()=>t.e(488).then(t.bind(t,6425))},{path:"/about",name:"about",component:()=>t.e(594).then(t.bind(t,1352))}],P=(0,k.aE)({history:(0,k.LA)("/"),routes:x});var O=P,T=t(782),S=(0,T.y$)({state:{},getters:{},mutations:{},actions:{},modules:{}}),$=t(1021),I=t(4373),U=t(1219),K=t(2933);const B=e=>{if(!e.response||!e.response.data)return e.message||"Unknown error";const n=e.response.data,t=[];n.exceptionMessage&&t.push(n.exceptionMessage);let o=n.innerException;while(o)o.exceptionMessage&&t.push(o.exceptionMessage),o=o.innerException;return 0===t.length?n.message||"An error occurred":t.join("<br>")},N=e=>{if(!e.response||!e.response.data)return void U.nk.error(e.message||"Unknown error");const n=B(e);K.s.alert(n,"Error",{confirmButtonText:"OK",dangerouslyUseHTMLString:!0,closeOnClickModal:!0,closeOnPressEscape:!0,showClose:!0})},z=e=>{if(e.response&&e.response.data){if("UserCanceled"===e.response.data)return!0;if("UserCanceled"===e.response.data.message)return!0;if("UserCanceled"===e.response.data.errorCode)return!0}return!1},Q=()=>{I.A.interceptors.response.use(e=>e,e=>z(e)?(U.nk.info("Operation cancelled by user"),Promise.reject(e)):(N(e),Promise.reject(e)))};var q=t(7854),D=(t(4188),t(2721)),W=t(7477),X=t(8950),G=t(2353),H=t(4996);X.Yv.add(G.Ubc,G.Uj9,G.QLR,G.h8M,G.Int,G.sjs,G.fny,G.a$,G.ao0,G.$Fj,G.qFF,G.Yj9,G.LqK,G.tdl,G.GF6,G.oZK,G.gr3,G.skf,G.DOu,G.v02,G._qq,G.iW_,G.Wzs,G.XkK,G.pS3,G.ijD,G.APi,G.Vpu,G.MjD,G.cbP,G.yLS,G.jTw,G.y_8,G.Bwz,H.Vz1);const V=(0,o.Ef)(b);V.component("font-awesome-icon",g.gc);for(const[R,Y]of Object.entries(W))V.component(R,Y);Q(),V.use(S).use(O).use(q.A,{locale:D.A,size:"default"}).mount("#app"),V.config.errorHandler=(e,n,t)=>{console.error("Vue 全局错误:",e);const o={message:e instanceof Error?e.message:String(e),stack:e instanceof Error?e.stack:"无堆栈信息",vueHookInfo:t,url:window.location.href};$.GQ.logError(o).catch(e=>{console.error("发送错误到服务器失败:",e)})},window.addEventListener("unhandledrejection",e=>{const n={message:e.reason instanceof Error?e.reason.message:"未处理的Promise异常",stack:e.reason instanceof Error?e.reason.stack:"无堆栈信息",url:window.location.href,type:"unhandledrejection"};$.GQ.logError(n).catch(e=>{console.error("发送Promise错误到服务器失败:",e)})}),window.addEventListener("error",e=>{if(e.message){const n={message:e.message,codeInfo:`${e.filename}:${e.lineno}:${e.colno}`,url:window.location.href,type:"global-error"};$.GQ.logError(n).catch(e=>{console.error("发送全局错误到服务器失败:",e)})}})}},n={};function t(o){var r=n[o];if(void 0!==r)return r.exports;var s=n[o]={exports:{}};return e[o].call(s.exports,s,s.exports,t),s.exports}t.m=e,function(){var e=[];t.O=function(n,o,r,s){if(!o){var a=1/0;for(u=0;u<e.length;u++){o=e[u][0],r=e[u][1],s=e[u][2];for(var i=!0,c=0;c<o.length;c++)(!1&s||a>=s)&&Object.keys(t.O).every(function(e){return t.O[e](o[c])})?o.splice(c--,1):(i=!1,s<a&&(a=s));if(i){e.splice(u--,1);var l=r();void 0!==l&&(n=l)}}return n}s=s||0;for(var u=e.length;u>0&&e[u-1][2]>s;u--)e[u]=e[u-1];e[u]=[o,r,s]}}(),function(){t.n=function(e){var n=e&&e.__esModule?function(){return e["default"]}:function(){return e};return t.d(n,{a:n}),n}}(),function(){t.d=function(e,n){for(var o in n)t.o(n,o)&&!t.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:n[o]})}}(),function(){t.f={},t.e=function(e){return Promise.all(Object.keys(t.f).reduce(function(n,o){return t.f[o](e,n),n},[]))}}(),function(){t.u=function(e){return"js/"+{488:"log-converter",594:"about"}[e]+"."+{488:"5c7da64c",594:"4124c382"}[e]+".js"}}(),function(){t.miniCssF=function(e){return"css/"+{488:"log-converter",594:"about"}[e]+"."+{488:"1c9b38c0",594:"81ed6e12"}[e]+".css"}}(),function(){t.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)}}(),function(){var e={},n="tab-kit-web:";t.l=function(o,r,s,a){if(e[o])e[o].push(r);else{var i,c;if(void 0!==s)for(var l=document.getElementsByTagName("script"),u=0;u<l.length;u++){var d=l[u];if(d.getAttribute("src")==o||d.getAttribute("data-webpack")==n+s){i=d;break}}i||(c=!0,i=document.createElement("script"),i.charset="utf-8",i.timeout=120,t.nc&&i.setAttribute("nonce",t.nc),i.setAttribute("data-webpack",n+s),i.src=o),e[o]=[r];var f=function(n,t){i.onerror=i.onload=null,clearTimeout(p);var r=e[o];if(delete e[o],i.parentNode&&i.parentNode.removeChild(i),r&&r.forEach(function(e){return e(t)}),n)return n(t)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=f.bind(null,i.onerror),i.onload=f.bind(null,i.onload),c&&document.head.appendChild(i)}}}(),function(){t.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){t.p="/"}(),function(){if("undefined"!==typeof document){var e=function(e,n,o,r,s){var a=document.createElement("link");a.rel="stylesheet",a.type="text/css",t.nc&&(a.nonce=t.nc);var i=function(t){if(a.onerror=a.onload=null,"load"===t.type)r();else{var o=t&&t.type,i=t&&t.target&&t.target.href||n,c=new Error("Loading CSS chunk "+e+" failed.\n("+o+": "+i+")");c.name="ChunkLoadError",c.code="CSS_CHUNK_LOAD_FAILED",c.type=o,c.request=i,a.parentNode&&a.parentNode.removeChild(a),s(c)}};return a.onerror=a.onload=i,a.href=n,o?o.parentNode.insertBefore(a,o.nextSibling):document.head.appendChild(a),a},n=function(e,n){for(var t=document.getElementsByTagName("link"),o=0;o<t.length;o++){var r=t[o],s=r.getAttribute("data-href")||r.getAttribute("href");if("stylesheet"===r.rel&&(s===e||s===n))return r}var a=document.getElementsByTagName("style");for(o=0;o<a.length;o++){r=a[o],s=r.getAttribute("data-href");if(s===e||s===n)return r}},o=function(o){return new Promise(function(r,s){var a=t.miniCssF(o),i=t.p+a;if(n(a,i))return r();e(o,i,null,r,s)})},r={524:0};t.f.miniCss=function(e,n){var t={488:1,594:1};r[e]?n.push(r[e]):0!==r[e]&&t[e]&&n.push(r[e]=o(e).then(function(){r[e]=0},function(n){throw delete r[e],n}))}}}(),function(){var e={524:0};t.f.j=function(n,o){var r=t.o(e,n)?e[n]:void 0;if(0!==r)if(r)o.push(r[2]);else{var s=new Promise(function(t,o){r=e[n]=[t,o]});o.push(r[2]=s);var a=t.p+t.u(n),i=new Error,c=function(o){if(t.o(e,n)&&(r=e[n],0!==r&&(e[n]=void 0),r)){var s=o&&("load"===o.type?"missing":o.type),a=o&&o.target&&o.target.src;i.message="Loading chunk "+n+" failed.\n("+s+": "+a+")",i.name="ChunkLoadError",i.type=s,i.request=a,r[1](i)}};t.l(a,c,"chunk-"+n,n)}},t.O.j=function(n){return 0===e[n]};var n=function(n,o){var r,s,a=o[0],i=o[1],c=o[2],l=0;if(a.some(function(n){return 0!==e[n]})){for(r in i)t.o(i,r)&&(t.m[r]=i[r]);if(c)var u=c(t)}for(n&&n(o);l<a.length;l++)s=a[l],t.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return t.O(u)},o=self["webpackChunktab_kit_web"]=self["webpackChunktab_kit_web"]||[];o.forEach(n.bind(null,0)),o.push=n.bind(null,o.push.bind(o))}();var o=t.O(void 0,[504],function(){return t(8579)});o=t.O(o)})();
//# sourceMappingURL=app.8897a52c.js.map