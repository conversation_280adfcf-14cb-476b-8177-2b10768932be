{"version": 3, "file": "DevToolsPlugin.js", "sourceRoot": "", "sources": ["../../../../../forked/third_party/devtools-frontend/src/extensions/edge_unminification_extension/DevToolsPlugin.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,qDAAqD;AACrD,yEAAyE;AACzE,6BAA6B;AAE7B,OAAO,EAAC,kBAAkB,EAAC,MAAM,yBAAyB,CAAC;AAC3D,OAAO,KAAK,EAAE,MAAM,wCAAwC,CAAC;AAE7D,MAAM,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC;AAEvB,SAAS,KAAK,CAAC,QAAgB,EAAE,MAAc;IAC7C,GAAG,CAAC,gBAAgB,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,CAAC,YAAY,CAAC,MAAM,EAAE,qBAAqB,CAAC,IAAI,CAAC,CAAC;IAC5F,MAAM,QAAQ,GAAG,YAAY,QAAQ,EAAE,CAAC;IACxC,MAAM,WAAW,GAAG,GAAG,QAAQ,MAAM,CAAC;IACtC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC3B,MAAM,IAAI,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;IACnC,MAAM,QAAQ,GAAG,GAAG,CAAC,gBAAgB,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,CAAC,YAAY,CAAC,MAAM,EAAE,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACnH,MAAM,MAAM,GAAG,SAAS,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC7C,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC9B,WAAW,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;IACrD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,WAAW,CAAC,QAAgB;IACnC,MAAM,OAAO,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;IACvC,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QAC7B,OAAO,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC;IAC5B,CAAC;IAED,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;QAC5B,OAAO,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;IAC3B,CAAC;IAED,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QAC7B,OAAO,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC;IAC5B,CAAC;IAED,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QACpF,OAAO,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;IAC3B,CAAC;IACD,OAAO,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,CAAE,gBAAgB;AAC7C,CAAC;AAED,SAAS,SAAS,CAAC,MAA6B,EAAE,QAAgB;IAChE,MAAM,WAAW,GAAyB,EAAE,CAAC;IAE7C,MAAM,IAAI,GAAG,eAAe,QAAQ,EAAE,CAAC;IACvC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IAEzD,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;QACzC,kBAAkB,CAAC,WAAW,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IACjD,CAAC;IAED,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAS,kBAAkB,CAAC,IAA0B,EAAE,IAAqB,EAAE,MAA6B;IAC1G,IAAI,GAAG,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC;QAClG,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC;QAC1F,GAAG,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,wBAAwB,CAAC,IAAI,CAAC,EAAE,CAAC;QACxG,qBAAqB,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IAC5C,CAAC;IAED,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;QACvC,kBAAkB,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IAC1C,CAAC;AACH,CAAC;AAED,SAAS,qBAAqB,CAC1B,IAA0B,EAC1B,IAAwC,EACxC,MAA6B;IAE/B,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;QACd,MAAM,IAAI,GAAG,kCAAkC,CAAC,IAAI,CAAC,CAAC;QACtD,MAAM,UAAU,GAAG,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QACxD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACxB,CAAC;AACH,CAAC;AAED,SAAS,gBAAgB,CAAC,IAAY,EAAE,KAA2B,EAAE,MAA6B;IAChG,MAAM,EAAC,GAAG,EAAE,GAAG,EAAC,GAAG,KAAK,CAAC;IACzB,MAAM,EAAC,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAC,GAAG,MAAM,CAAC,6BAA6B,CAAC,GAAG,CAAC,CAAC;IAC5F,MAAM,EAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAC,GAAG,MAAM,CAAC,6BAA6B,CAAC,GAAG,CAAC,CAAC;IAExF,OAAO,IAAI,kBAAkB,CAAC,IAAI,EAAE,EAAC,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW,EAAC,EAAE,EAAC,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAC,CAAC,CAAC;AAClH,CAAC;AAED,SAAS,kCAAkC,CAAC,IAAwC;IAClF,IAAI,IAAI,GAAG,mBAAmB,CAAC;IAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;IAC3B,IAAI,QAAQ,EAAE,CAAC;QACb,uEAAuE;QACvE;;;;;;;;;WASG;QACH,IAAI,GAAG,iBAAiB,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACjD,CAAC;SAAM,IAAI,GAAG,CAAC,wBAAwB,CAAC,IAAI,CAAC,EAAE,CAAC;QAC9C;;;;WAIG;QACH,8BAA8B;QAC9B,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC;QACpC,IAAI,GAAG,CAAC,kBAAkB,CAAC,eAAe,CAAC,EAAE,CAAC;YAC5C,IAAI,SAAS,GAAG,gBAAgB,CAAC;YACjC,IAAI,eAAe,CAAC,IAAI,EAAE,CAAC;gBACzB,SAAS,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;YACxC,CAAC;YACD,IAAI,GAAG,qBAAqB,SAAS,EAAE,CAAC;QAC1C,CAAC;IACH,CAAC;SAAM,CAAC;QACN;;;;;;;;;;WAUG;QAEH,IAAI,GAAG,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;YAChE,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YACzB,sBAAsB;YACtB,IAAI,GAAG,CAAC,yBAAyB,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC1C,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;YACzB,CAAC;YAED,IAAI,GAAG,CAAC,qBAAqB,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC/G,IAAI,MAAM,CAAC,IAAI,IAAI,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;oBACjD,IAAI,GAAG,iBAAiB,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;gBACpD,CAAC;YACH,CAAC;iBAAM,IAAI,GAAG,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,aAAa,CAAC,IAAI,KAAK,GAAG,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;gBACtG,IAAI,GAAG,CAAC,0BAA0B,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC9F,IAAI,GAAG,gCAAgC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACvD,CAAC;qBAAM,IACH,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC3G,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC1B,CAAC;gBACD,eAAe;YACjB,CAAC;iBAAM,IAAI,GAAG,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;gBAClF,IAAI,oBAAoB,GAAG,gCAAgC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;gBACpF,IAAI,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;oBACrC,6EAA6E;oBAC7E,oBAAoB,GAAG,OAAO,oBAAoB,EAAE,CAAC;gBACvD,CAAC;gBACD,IAAI,GAAG,wBAAwB,oBAAoB,EAAE,CAAC;YACxD,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,gCAAgC,CAAC,UAAiC;IACzE,IAAI,GAAG,CAAC,0BAA0B,CAAC,UAAU,CAAC,EAAE,CAAC;QAC/C,OAAO,GAAG,gCAAgC,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IAC9F,CAAC;IAED,IAAI,GAAG,CAAC,yBAAyB,CAAC,UAAU,CAAC,EAAE,CAAC;QAC9C,OAAO,GAAG,gCAAgC,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,kBAAkB,GAAG,CAAC;IACxG,CAAC;IAED,IAAI,GAAG,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,CAAC;QACrC,OAAO,UAAU,CAAC,OAAO,EAAE,CAAC;IAC9B,CAAC;IAED,IAAI,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,CAAC;QACxG,OAAO,UAAU,CAAC,IAAI,CAAC;IACzB,CAAC;IAED,OAAO,kBAAkB,CAAC;AAC5B,CAAC;AAED,SAAS,iBAAiB,CACtB,QAAiC,EAAE,aAA8B,EAAE,QAAgB;IACrF,IAAI,QAAQ,GAAG,QAAQ,CAAC;IACxB,QAAQ,QAAQ,CAAC,IAAI,EAAE,CAAC;QACtB,KAAK,GAAG,CAAC,UAAU,CAAC,oBAAoB;YACtC,IAAI,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC1C,QAAQ,GAAG,IAAI,QAAQ,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC;YAC7C,CAAC;iBAAM,IAAI,GAAG,CAAC,eAAe,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBACjG,QAAQ,GAAG,IAAI,QAAQ,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC;YAC7C,CAAC;iBAAM,CAAC;gBACN,QAAQ,GAAG,kBAAkB,CAAC;YAChC,CAAC;YACD,MAAM;QAER,KAAK,GAAG,CAAC,UAAU,CAAC,aAAa,CAAC;QAClC,KAAK,GAAG,CAAC,UAAU,CAAC,cAAc,CAAC;QACnC,KAAK,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC;QAC/B,KAAK,GAAG,CAAC,UAAU,CAAC,iBAAiB;YACnC,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC;YACzB,MAAM;IACV,CAAC;IAED,IAAI,GAAG,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,GAAG,CAAC,wBAAwB,CAAC,aAAa,CAAC,EAAE,CAAC;QACpF,QAAQ,GAAG,OAAO,QAAQ,EAAE,CAAC;IAC/B,CAAC;SAAM,IAAI,GAAG,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,GAAG,CAAC,aAAa,CAAC,aAAa,CAAC,EAAE,CAAC;QAChF,QAAQ,GAAG,OAAO,QAAQ,EAAE,CAAC;IAC/B,CAAC;IAED,IAAI,aAAa,CAAC,MAAM,IAAI,GAAG,CAAC,kBAAkB,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC;QACzE,IAAI,SAAS,GAAG,iBAAiB,CAAC;QAClC,IAAI,aAAa,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YAC9B,SAAS,GAAG,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;QAC7C,CAAC;QAED,QAAQ,GAAG,GAAG,SAAS,IAAI,QAAQ,EAAE,CAAC;IACxC,CAAC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAS,qBAAqB,CAAC,OAAoC;IACjE,IAAI,OAAO,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;QAC3D,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;QACtC,OAAO,GAAG,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE,QAAQ,CAAC,MAAM,CAAC,IAAI,GAAG,EAAE,QAAQ,CAAC,MAAM,CAAC;YACjG,GAAG,EAAE,QAAQ,CAAC,MAAM,CAAC,IAAI,GAAG,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;IACrD,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,aAAa;AACb,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,eAAe,CAAC,WAAW,CAAC,KAAK,EAAC,QAAQ,EAAC,EAAE;IAC3E,IAAI,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC;QACpC,MAAM,cAAc,GAAG,MAAM,IAAI,OAAO,CACpC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,OAAY,EAAE,QAAa,EAAE,EAAE,CAAC,CAAC,CAAC,EAAC,GAAG,EAAE,QAAQ,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,EAAC,CAAC,CAAC,CAAC,CAAC;QAC1G,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;YAC3B,MAAM,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,cAAc,CAAC,OAAO,CAAC,CAAC;YAC3D,IAAI,CAAC;gBACH,MAAM,CAAC,QAAQ,CAAC,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;YACtD,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,OAAO,CAAC,CAAC;YACX,CAAC;QACH,CAAC;IACH,CAAC;AACH,CAAC,CAAC,CAAC", "sourcesContent": ["// Copyright 2025 The Chromium Authors. All rights reserved.\n// Copyright (C) Microsoft Corp. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nimport {NamedFunctionRange} from './NamedFunctionRange.js';\nimport * as ts from './third_party/typescript/typescript.js';\n\nconst tsc = ts.default;\n\nfunction parse(fileName: string, source: string): NamedFunctionRange[] {\n  tsc.createSourceFile(fileName, source, tsc.ScriptTarget.ESNext, /* setParentNodes: */ true);\n  const markName = `parsing: ${fileName}`;\n  const endMarkName = `${markName}-end`;\n  performance.mark(markName);\n  const kind = getFileType(fileName);\n  const tsSource = tsc.createSourceFile(fileName, source, tsc.ScriptTarget.ESNext, /* setParentNodes: */ true, kind);\n  const result = visitRoot(tsSource, fileName);\n  performance.mark(endMarkName);\n  performance.measure(fileName, markName, endMarkName);\n  return result;\n}\n\nfunction getFileType(fileName: string) {\n  const lowered = fileName.toLowerCase();\n  if (lowered.endsWith('.tsx')) {\n    return tsc.ScriptKind.TSX;\n  }\n\n  if (lowered.endsWith('.ts')) {\n    return tsc.ScriptKind.TS;\n  }\n\n  if (lowered.endsWith('.jsx')) {\n    return tsc.ScriptKind.JSX;\n  }\n\n  if (lowered.endsWith('.js') || lowered.endsWith('.cjs') || lowered.endsWith('.mjs')) {\n    return tsc.ScriptKind.JS;\n  }\n  return tsc.ScriptKind.TS;  // default to ts\n}\n\nfunction visitRoot(source: ts.default.SourceFile, fileName: string): NamedFunctionRange[] {\n  const accumulator: NamedFunctionRange[] = [];\n\n  const name = `globalCode: ${fileName}`;\n  accumulator.push(createDescriptor(name, source, source));\n\n  for (const child of source.getChildren()) {\n    visitNodeIterative(accumulator, child, source);\n  }\n\n  return accumulator;\n}\n\nfunction visitNodeIterative(dest: NamedFunctionRange[], node: ts.default.Node, source: ts.default.SourceFile): void {\n  if (tsc.isFunctionDeclaration(node) || tsc.isFunctionExpression(node) || tsc.isMethodDeclaration(node) ||\n      tsc.isArrowFunction(node) || tsc.isConstructorDeclaration(node) || tsc.isGetAccessor(node) ||\n      tsc.isGetAccessorDeclaration(node) || tsc.isSetAccessor(node) || tsc.isSetAccessorDeclaration(node)) {\n    visitFunctionNodeImpl(dest, node, source);\n  }\n\n  for (const child of node.getChildren()) {\n    visitNodeIterative(dest, child, source);\n  }\n}\n\nfunction visitFunctionNodeImpl(\n    dest: NamedFunctionRange[],\n    node: ts.default.FunctionLikeDeclaration,\n    source: ts.default.SourceFile,\n    ): void {\n  if (node.body) {\n    const name = getNamesForFunctionLikeDeclaration(node);\n    const descriptor = createDescriptor(name, node, source);\n    dest.push(descriptor);\n  }\n}\n\nfunction createDescriptor(name: string, range: ts.default.TextRange, source: ts.default.SourceFile) {\n  const {pos, end} = range;\n  const {line: startLine, character: startColumn} = source.getLineAndCharacterOfPosition(pos);\n  const {line: endLine, character: endColumn} = source.getLineAndCharacterOfPosition(end);\n\n  return new NamedFunctionRange(name, {line: startLine, column: startColumn}, {line: endLine, column: endColumn});\n}\n\nfunction getNamesForFunctionLikeDeclaration(func: ts.default.FunctionLikeDeclaration): string {\n  let name = 'anonymousFunction';\n  const nameNode = func.name;\n  if (nameNode) {\n    // named function, property name, identifier, string, computed property\n    /**\n     * function foo() {}   <--\n     * class Sample {\n     *   constructor() { }   NOT this one\n     *   bar() { }    <--\n     *   get baz() { }    <--\n     *   set frob() { }    <--\n     *   [Symbol.toString]()    <--\n     * }\n     */\n    name = getNameOfNameNode(nameNode, func, name);\n  } else if (tsc.isConstructorDeclaration(func)) {\n    /**\n     * class Sample {\n     *   constructor() { }   <--\n     * }\n     */\n    // (constructor for class Foo)\n    const classDefinition = func.parent;\n    if (tsc.isClassDeclaration(classDefinition)) {\n      let className = 'anonymousClass';\n      if (classDefinition.name) {\n        className = classDefinition.name.text;\n      }\n      name = `constructorCall:, ${className}`;\n    }\n  } else {\n    /**\n     * const x = function() { }\n     * const y = () => { }\n     * const z = {\n     *  frob: function() { },\n     *  florbo: () => { },\n     * }\n     *\n     * doSomething(function() { })\n     * doSomething(() => { })\n     */\n\n    if (tsc.isFunctionExpression(func) || tsc.isArrowFunction(func)) {\n      let parent = func.parent;\n      // e.g., ( () => { } )\n      if (tsc.isParenthesizedExpression(parent)) {\n        parent = parent.parent;\n      }\n\n      if (tsc.isVariableDeclaration(parent) || tsc.isPropertyAssignment(parent) || tsc.isPropertyDeclaration(parent)) {\n        if (parent.name && tsc.isIdentifier(parent.name)) {\n          name = getNameOfNameNode(parent.name, func, name);\n        }\n      } else if (tsc.isBinaryExpression(parent) && parent.operatorToken.kind === tsc.SyntaxKind.EqualsToken) {\n        if (tsc.isPropertyAccessExpression(parent.left) || tsc.isElementAccessExpression(parent.left)) {\n          name = recursivelyGetPropertyAccessName(parent.left);\n        } else if (\n            tsc.isIdentifier(parent.left) || tsc.isStringLiteral(parent.left) || tsc.isNumericLiteral(parent.left)) {\n          name = parent.left.text;\n        }\n        // else unknown\n      } else if (tsc.isCallOrNewExpression(func.parent) || tsc.isDecorator(func.parent)) {\n        let parentExpressionName = recursivelyGetPropertyAccessName(func.parent.expression);\n        if (tsc.isNewExpression(func.parent)) {\n          // Localization is not required: this is a programming expression (\"new Foo\")\n          parentExpressionName = `new ${parentExpressionName}`;\n        }\n        name = `anonymousCallbackTo: ${parentExpressionName}`;\n      }\n    }\n  }\n  return name;\n}\n\nfunction recursivelyGetPropertyAccessName(expression: ts.default.Expression): string {\n  if (tsc.isPropertyAccessExpression(expression)) {\n    return `${recursivelyGetPropertyAccessName(expression.expression)}.${expression.name.text}`;\n  }\n\n  if (tsc.isElementAccessExpression(expression)) {\n    return `${recursivelyGetPropertyAccessName(expression.expression)}[${expression.argumentExpression}]`;\n  }\n\n  if (tsc.isCallExpression(expression)) {\n    return expression.getText();\n  }\n\n  if (tsc.isIdentifier(expression) || tsc.isStringLiteral(expression) || tsc.isNumericLiteral(expression)) {\n    return expression.text;\n  }\n\n  return 'computedProperty';\n}\n\nfunction getNameOfNameNode(\n    nameNode: ts.default.PropertyName, declaringNode: ts.default.Node, fallback: string): string {\n  let nameText = fallback;\n  switch (nameNode.kind) {\n    case tsc.SyntaxKind.ComputedPropertyName:\n      if (tsc.isIdentifier(nameNode.expression)) {\n        nameText = `[${nameNode.expression.text}]`;\n      } else if (tsc.isStringLiteral(nameNode.expression) || tsc.isNumericLiteral(nameNode.expression)) {\n        nameText = `[${nameNode.expression.text}]`;\n      } else {\n        nameText = 'computedProperty';\n      }\n      break;\n\n    case tsc.SyntaxKind.StringLiteral:\n    case tsc.SyntaxKind.NumericLiteral:\n    case tsc.SyntaxKind.Identifier:\n    case tsc.SyntaxKind.PrivateIdentifier:\n      nameText = nameNode.text;\n      break;\n  }\n\n  if (tsc.isGetAccessor(declaringNode) || tsc.isGetAccessorDeclaration(declaringNode)) {\n    nameText = `get ${nameText}`;\n  } else if (tsc.isSetAccessor(declaringNode) || tsc.isSetAccessor(declaringNode)) {\n    nameText = `set ${nameText}`;\n  }\n\n  if (declaringNode.parent && tsc.isClassDeclaration(declaringNode.parent)) {\n    let className = 'anonymousClass)';\n    if (declaringNode.parent.name) {\n      className = declaringNode.parent.name.text;\n    }\n\n    nameText = `${className}.${nameText}`;\n  }\n  return nameText;\n}\n\nfunction isSourceMapScriptFile(resouce: {url: string, type: string}) {\n  if (resouce && resouce.url && resouce.type === 'sm-script') {\n    const url = resouce.url.toLowerCase();\n    return url?.endsWith('.js') || url?.endsWith('.ts') || url?.endsWith('.jsx') || url?.endsWith('.tsx') ||\n        url?.endsWith('.mjs') || url?.endsWith('.cjs');\n  }\n  return false;\n}\n\n// @ts-ignore\nchrome.devtools.inspectedWindow.onResourceAdded.addListener(async resource => {\n  if (isSourceMapScriptFile(resource)) {\n    const scriptResource = await new Promise<{url: string, content?: string, encoding?: string}>(\n        r => resource.getContent((content: any, encoding: any) => r({url: resource.url, content, encoding})));\n    if (scriptResource.content) {\n      const ranges = parse(resource.url, scriptResource.content);\n      try {\n        await (resource).setFunctionRangesForScript(ranges);\n      } catch (e) {\n        return e;\n      }\n    }\n  }\n});\n"]}