"use strict";(self["webpackChunktab_kit_web"]=self["webpackChunktab_kit_web"]||[]).push([[594],{1352:function(a,s,c){c.r(s),c.d(s,{default:function(){return b}});var e=c(6768);const l={class:"about"},n={class:"page-header"},t={class:"app-logo"},i={class:"app-info"},k={class:"card-header"},o={class:"card-header"},d={class:"card-header"},L={class:"contact-item"};function h(a,s,c,h,u,p){const r=(0,e.g2)("font-awesome-icon"),v=(0,e.g2)("el-card");return(0,e.uX)(),(0,e.CE)("div",l,[(0,e.Lk)("div",n,[(0,e.Lk)("div",t,[(0,e.bF)(r,{icon:"cogs",class:"logo-icon"})]),s[0]||(s[0]=(0,e.Lk)("h1",null,"TabKit",-1)),s[1]||(s[1]=(0,e.Lk)("p",{class:"version"},"版本 1.0.0",-1))]),(0,e.Lk)("div",i,[(0,e.bF)(v,{class:"info-card"},{header:(0,e.k6)(()=>[(0,e.Lk)("div",k,[(0,e.bF)(r,{icon:"info-circle"}),s[2]||(s[2]=(0,e.Lk)("span",null,"应用信息",-1))])]),default:(0,e.k6)(()=>[s[3]||(s[3]=(0,e.Lk)("div",{class:"info-content"},[(0,e.Lk)("p",{class:"description"}," Alsi.Tab.Kit 是一个集成多种实用工具的桌面应用程序，旨在提高工作效率和简化日常任务。 ")],-1))]),_:1,__:[3]}),(0,e.bF)(v,{class:"info-card"},{header:(0,e.k6)(()=>[(0,e.Lk)("div",o,[(0,e.bF)(r,{icon:"code"}),s[4]||(s[4]=(0,e.Lk)("span",null,"技术信息",-1))])]),default:(0,e.k6)(()=>[s[5]||(s[5]=(0,e.Lk)("div",{class:"tech-info"},[(0,e.Lk)("div",{class:"tech-grid"},[(0,e.Lk)("div",{class:"tech-item"},[(0,e.Lk)("span",{class:"tech-label"},"前端框架:"),(0,e.Lk)("span",{class:"tech-value"},"Vue 3 + TypeScript")]),(0,e.Lk)("div",{class:"tech-item"},[(0,e.Lk)("span",{class:"tech-label"},"UI组件库:"),(0,e.Lk)("span",{class:"tech-value"},"Element Plus")]),(0,e.Lk)("div",{class:"tech-item"},[(0,e.Lk)("span",{class:"tech-label"},"图标库:"),(0,e.Lk)("span",{class:"tech-value"},"FontAwesome")]),(0,e.Lk)("div",{class:"tech-item"},[(0,e.Lk)("span",{class:"tech-label"},"后端框架:"),(0,e.Lk)("span",{class:"tech-value"},".NET Framework")]),(0,e.Lk)("div",{class:"tech-item"},[(0,e.Lk)("span",{class:"tech-label"},"桌面框架:"),(0,e.Lk)("span",{class:"tech-value"},"WPF + WebView2")]),(0,e.Lk)("div",{class:"tech-item"},[(0,e.Lk)("span",{class:"tech-label"},"构建工具:"),(0,e.Lk)("span",{class:"tech-value"},"Vue CLI + Webpack")])])],-1))]),_:1,__:[5]}),(0,e.bF)(v,{class:"info-card"},{header:(0,e.k6)(()=>[(0,e.Lk)("div",d,[(0,e.bF)(r,{icon:"envelope"}),s[6]||(s[6]=(0,e.Lk)("span",null,"联系信息",-1))])]),default:(0,e.k6)(()=>[(0,e.Lk)("div",L,[s[7]||(s[7]=(0,e.Lk)("span",null,"如果您在使用过程中遇到问题或有任何建议，欢迎联系：",-1)),(0,e.bF)(r,{icon:"envelope",class:"contact-icon"}),s[8]||(s[8]=(0,e.Lk)("a",{href:"mailto:<EMAIL>",class:"contact-link"},"<EMAIL>",-1))])]),_:1})])])}var u=c(292),p=(0,e.pM)({name:"AboutView",components:{FontAwesomeIcon:u.gc}}),r=c(1241);const v=(0,r.A)(p,[["render",h],["__scopeId","data-v-ec54dc2e"]]);var b=v}}]);
//# sourceMappingURL=about.4124c382.js.map