{"aadc_info": {"age_group": 0}, "accessibility": {"captions": {"headless_caption_enabled": false}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "autocomplete": {"retention_policy_last_version": 137}, "autofill": {"last_version_deduped": 137}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"app_window_placement": {"EdgeDevToolsApp": {"always_on_top": false, "bottom": 824, "left": 756, "maximized": true, "right": 1544, "top": 180, "work_area_bottom": 816, "work_area_left": 0, "work_area_right": 1536, "work_area_top": 0}}, "available_dark_theme_options": "All", "enable_spellchecking": true, "has_seen_welcome_page": false, "recent_theme_color_list": [**********.0, **********.0, **********.0, **********.0, **********.0], "user_level_features_context": {}}, "browser_content_container_height": 793, "browser_content_container_width": 1536, "browser_content_container_x": 0, "browser_content_container_y": 23, "collections": {"prism_collections": {"policy": {"cached": 0}}}, "commerce_daily_metrics_last_update_time": "13394864230011462", "countryid_at_install": 17230, "credentials_enable_service": false, "devtools": {"last_open_timestamp": "13394875196922", "preferences": {"EdgeDevToolsLayoutInfo": {"current_dock_state": 0, "horizontal_size": 300, "showEmulationMode": false, "vertical_size": 555}, "closeable-tabs": "{\"security\":true,\"heap-profiler\":true,\"resources\":true,\"lighthouse\":true,\"welcome\":true,\"timeline\":true,\"network\":true,\"cssoverview\":true,\"issues-pane\":true}", "cloud-release-notes": "{\"edgeVersion\":137,\"shouldOpenWelcome\":true,\"help\":[{\"title\":\"DevTools documentation\",\"linkId\":\"2196640\",\"localizedAnnouncementKey\":\"helpCard1\",\"iconName\":\"edge-documentation_book_filled\"},{\"title\":\"Overview of all tools\",\"linkId\":\"2196549\",\"localizedAnnouncementKey\":\"helpCard2\",\"iconName\":\"edge-developer-resources\"},{\"title\":\"Use Copilot to explain Console errors\",\"linkId\":\"2257416\",\"localizedAnnouncementKey\":\"helpCard3\",\"iconName\":\"edge-copilot\"},{\"title\":\"Videos about web development with Microsoft Edge\",\"linkId\":\"2196701\",\"localizedAnnouncementKey\":\"helpCard5\",\"iconName\":\"edge-run_command\"},{\"title\":\"Accessibility testing features\",\"linkId\":\"2196801\",\"localizedAnnouncementKey\":\"helpCard6\",\"iconName\":\"edge-documentation_book_filled\"},{\"title\":\"Use the Console tool to track down problems\",\"linkId\":\"2196702\",\"localizedAnnouncementKey\":\"helpCard7\",\"iconName\":\"edge-console\"},{\"title\":\"Modify and debug JS with the Sources tool\",\"linkId\":\"2196900\",\"localizedAnnouncementKey\":\"helpCard8\",\"iconName\":\"edge-sources\"},{\"title\":\"Find source files for a page using the search tool\",\"linkId\":\"2196802\",\"localizedAnnouncementKey\":\"helpCard9\",\"iconName\":\"edge-sources-search-sources-tab\"},{\"title\":\"Microsoft Edge DevTools for Visual Studio Code\",\"linkId\":\"2196901\",\"localizedAnnouncementKey\":\"helpCard10\",\"iconName\":\"edge-help_tooltips\"}],\"releaseNotes\":[{\"title\":\"Sync your DevTools settings between devices\",\"subtitle\":\"Enable settings sync lets you sync your DevTools settings across devices.\",\"linkId\":\"2324701\",\"localizedAnnouncementKey\":\"edgeAnnouncement1\"}],\"header\":{\"localizedKey\":\"highlightsFromTheLatestMicrosoft\",\"title\":\"What's New\"},\"learnHeader\":{\"localizedKey\":\"learnHeader\",\"title\":\"Learn\"},\"allAnnouncementsLinkText\":{\"localizedKey\":\"allAnnouncementsLinkText\",\"title\":\"View all\"},\"whatsNewVideo\":{\"title\":\"What's New in DevTools 115 - 125\",\"subtitle\":\"Check out our video series on the latest and greatest features in DevTools!\",\"linkId\":\"26zDq9Xhz7k\",\"imageName\":\"whats-new-115-125-thumbnail.jpg\",\"imageAltText\":\"A title card for the Microsoft Edge: What's New in DevTools 115 - 125 video\",\"localizedKey\":\"whatsNewVideo\"},\"viewAllLinkId\":\"2324502\",\"localized\":{\"en-US\":{\"panels/edge_welcome/ReleaseNotes.ts | helpCard1\":{\"message\":\"DevTools documentation\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | helpCard2\":{\"message\":\"Overview of all tools\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | helpCard3\":{\"message\":\"Use Copilot to explain Console errors\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | helpCard5\":{\"message\":\"Videos about web development with Microsoft Edge\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | helpCard6\":{\"message\":\"Accessibility testing features\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | helpCard7\":{\"message\":\"Use the Console tool to track down problems\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | helpCard8\":{\"message\":\"Modify and debug JS with the Sources tool\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | helpCard9\":{\"message\":\"Find source files for a page using the search tool\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | helpCard10\":{\"message\":\"Microsoft Edge DevTools for Visual Studio Code\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | edgeAnnouncement1\":{\"message\":\"Sync your DevTools settings between devices\",\"description\":\"Title of a release note, shown next to a description, in a list of release notes.\"},\"panels/edge_welcome/ReleaseNotes.ts | edgeAnnouncement1Description\":{\"message\":\"Enable settings sync lets you sync your DevTools settings across devices.\",\"description\":\"Description of a release note providing further details, shown next to each release note title.\"},\"panels/edge_welcome/ReleaseNotes.ts | learnHeader\":{\"message\":\"Learn\",\"description\":\"Title text of a header bar in the welcome tool.\"},\"panels/edge_welcome/ReleaseNotes.ts | allAnnouncementsLinkText\":{\"message\":\"View all\",\"description\":\"Title text of a header bar in the welcome tool.\"},\"panels/edge_welcome/ReleaseNotes.ts | highlightsFromTheLatestMicrosoft\":{\"message\":\"What's New\",\"description\":\"Title text of a header bar in the welcome tool.\"},\"panels/edge_welcome/ReleaseNotes.ts | whatsNewVideo\":{\"message\":\"What's New in DevTools 115 - 125\",\"description\":\"Title of a video summarizing the latest release, shown next to a description, above a list of release notes.\"},\"panels/edge_welcome/ReleaseNotes.ts | whatsNewVideoDescription\":{\"message\":\"Check out our video series on the latest and greatest features in DevTools!\",\"description\":\"Description of a video link providing further details\"}}}}", "console.sidebar-selected-filter": "\"message\"", "console.sidebar.width": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "drawer-minimize-state": "false", "edge-inspector.actions-tab-split-view-state": "{\"vertical\":{\"size\":0,\"showMode\":\"Both\"},\"horizontal\":{\"size\":30,\"showMode\":\"Both\"}}", "elements-panel-split-view-state": "{\"vertical\":{\"size\":437}}", "elements.styles.sidebar.width": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "inspector.drawer-split-view-state": "{\"horizontal\":{\"size\":0,\"showMode\":\"Both\"}}", "inspectorVersion": "38", "last-open-view-in-drawer": "\"console\"", "network-panel-sidebar-state": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "network-panel-split-view-state": "{\"vertical\":{\"size\":0}}", "network-panel-split-view-waterfall": "{\"vertical\":{\"size\":0}}", "panel-selected-tab": "\"elements\"", "panel-tab-order": "{\"welcome\":10,\"elements\":20,\"sources\":30,\"network\":40,\"timeline\":50,\"heap-profiler\":60,\"resources\":70,\"security\":80,\"lighthouse\":90,\"cssoverview\":100,\"console\":110}", "release-note-version-seen": "137", "request-info-form-data-category-expanded": "true", "request-info-general-category-expanded": "true", "request-info-query-string-category-expanded": "true", "request-info-request-headers-category-expanded": "true", "request-info-request-payload-category-expanded": "true", "request-info-response-headers-category-expanded": "true", "resource-view-tab": "\"response\"", "sources-panel-navigator-split-view-state": "{\"vertical\":{\"size\":0,\"showMode\":\"Both\"}}", "sources-panel-split-view-state": "{\"vertical\":{\"size\":0,\"showMode\":\"Both\"},\"horizontal\":{\"size\":0,\"showMode\":\"Both\"}}", "styles-pane-sidebar-selected-tab": "\"styles\"", "styles-pane-sidebar-tab-order": "{\"styles\":10,\"computed\":20}", "tools-used": "{\"welcome\":1750390912310,\"console-view\":1750401597182,\"sources\":1750390912389,\"network\":1750400662610,\"elements\":1750401685914,\"console\":1750399452695}", "undefined-tab-order": "{\"sources.scope-chain\":10,\"sources.watch\":20}"}, "synced_preferences_sync_disabled": {"adorner-settings": "[{\"adorner\":\"grid\",\"isEnabled\":true},{\"adorner\":\"subgrid\",\"isEnabled\":true},{\"adorner\":\"flex\",\"isEnabled\":true},{\"adorner\":\"ad\",\"isEnabled\":true},{\"adorner\":\"scroll-snap\",\"isEnabled\":true},{\"adorner\":\"container\",\"isEnabled\":true},{\"adorner\":\"slot\",\"isEnabled\":true},{\"adorner\":\"top-layer\",\"isEnabled\":true},{\"adorner\":\"reveal\",\"isEnabled\":true},{\"adorner\":\"media\",\"isEnabled\":false},{\"adorner\":\"scroll\",\"isEnabled\":true}]", "syncedInspectorVersion": "38"}}, "domain_diversity": {"last_reporting_timestamp": "13394864230070572"}, "edge": {"bookmarks": {"last_dup_info_record_time": "13394864240329741"}, "msa_sso_info": {"allow_for_non_msa_profile": true}, "profile_sso_info": {"is_msa_first_profile": true, "msa_sso_algo_state": 1}, "services": {"signin_scoped_device_id": "8eee748d-855b-4716-8432-656b1e45f61e"}}, "edge_rewards": {"cache_data": "CAA=", "coachmark_promotions": {}, "hva_promotions": [], "refresh_status_muted_until": "13395469029993298"}, "edge_ux_config": {"assignmentcontext": "", "dataversion": "0", "experimentvariables": {}, "flights": {}}, "edge_wallet": {"passwords": {"password_lost_report_date": "*****************"}}, "enterprise_profile_guid": "81b06743-abb4-4d5d-9a07-be3d43b753c9", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "commands": {}, "last_chrome_version": "137.0.3296.83", "pdf_upsell_triggered": false, "pinned_extension_migration": true, "pinned_extensions": [], "settings": {"cgjgjfacjflmgphhhepmbhhbgjieaecn": {"account_extension_type": 0, "ack_external": true, "active_permissions": {"api": ["devtools"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [], "edge_last_update_check_time": "*****************", "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 10, "manifest": {"description": "Provides Named Function Ranges from typescript's compiler to augment sourcemap scopes information", "devtools_page": "DevToolsPlugin.html", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAiMfSIPlj0PRSUeFx85BNsj/QeZ3AhvP4ScF9UxY8S+OWRyP7RcqU0e5E2okxSBD4r+L0MerEVIaUPyuMCfY4gn+Cc0CPCw/EtG/17Z0Sx9PgiM71CgWa07TYXZQXQW+K32FWf5v35prF2m75SNOUG2b4J3HMf1YkCWhEi2URHmNKIIJjrABdm5mBUzLAMM5ZKAAK9voekfq4YETl58ClarnTjM7pKBw2NvrSSuZCj5llCQoZcdfUAkOBtHyXqhmjEiVVeO2du1jDlPuVPs3YqCM99Q+kTASfUfLSV3vosx1lonpghMj9CPcOxpQrI8ybqPY24b5sv4ULigpaZL6RLwIDAQAB", "manifest_version": 3, "name": "Microsoft Edge Unminification Extension", "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "135.0.3176.0"}, "path": "cgjgjfacjflmgphhhepmbhhbgjieaecn\\135.0.3176.0_0", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "dgiklkfkllikcanfonkcabmbdfmgleag": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_capabilities": {"include_globs": ["https://*excel.officeapps.live.com/*", "https://*onenote.officeapps.live.com/*", "https://*powerpoint.officeapps.live.com/*", "https://*word-edit.officeapps.live.com/*", "https://*excel.officeapps.live.com.mcas.ms/*", "https://*onenote.officeapps.live.com.mcas.ms/*", "https://*word-edit.officeapps.live.com.mcas.ms/*", "https://*excel.partner.officewebapps.cn/*", "https://*onenote.partner.officewebapps.cn/*", "https://*powerpoint.partner.officewebapps.cn/*", "https://*word-edit.partner.officewebapps.cn/*", "https://*excel.gov.online.office365.us/*", "https://*onenote.gov.online.office365.us/*", "https://*powerpoint.gov.online.office365.us/*", "https://*word-edit.gov.online.office365.us/*", "https://*excel.dod.online.office365.us/*", "https://*onenote.dod.online.office365.us/*", "https://*powerpoint.dod.online.office365.us/*", "https://*word-edit.dod.online.office365.us/*", "https://*visio.partner.officewebapps.cn/*", "https://*visio.gov.online.office365.us/*", "https://*visio.dod.online.office365.us/*"], "matches": ["https://*.officeapps.live.com/*", "https://*.officeapps.live.com.mcas.ms/*", "https://*.partner.officewebapps.cn/*", "https://*.gov.online.office365.us/*", "https://*.dod.online.office365.us/*", "https://*.app.whiteboard.microsoft.com/*", "https://*.whiteboard.office.com/*", "https://*.app.int.whiteboard.microsoft.com/*", "https://*.whiteboard.office365.us/*", "https://*.dev.whiteboard.microsoft.com/*"], "permissions": ["clipboardRead", "clipboardWrite"]}, "default_locale": "en", "description": "This extension grants Microsoft web sites permission to read and write from the clipboard.", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCz4t/X7GeuP6GBpjmxndrjtzF//4CWeHlC68rkoV7hP3h5Ka6eX7ZMNlYJkSjmB5iRmPHO5kR1y7rGY8JXnRPDQh/CQNLVA7OsKeV6w+UO+vx8KGI+TrTAhzH8YGcMIsxsUjxtC4cBmprja+xDr0zVp2EMgqHu+GBKgwSRHTkDuwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "77", "name": "Microsoft Clipboard Extension", "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\137.0.3296.83\\resources\\edge_clipboard", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "kfbdpdaobnofkbopebjglnaadopfikhh": {"account_extension_type": 0, "ack_external": true, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [], "edge_last_update_check_time": "*****************", "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 10, "manifest": {"description": "Microsoft Edge DevTools Enhancements", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxx2oBf3foCCxdn8gQWEGh6HQhGfz+kbpzYJSgAiMy8T6NFVYRfECBK/oZad9hKR317bgpyQlAeaueDu7K2f1NtRKVKA/RCiYUcDp9hyaDmoXn0+ayis97+1Rvl13IGToAqxehQ9T8ZNz4B1uRegJpNHpKA9LCW4uUh6iTC0hMKKTfEXMUVZQ6uQEeXRb+YpB7ZlesFcEZvnbbs2yj4BvjOXWaaxxWTJE0f3hu2dAPgQ4YMp3wluI7eKH475okTdJsdSR4yfcMwx9UHLqp6tUTENAUrb724HWF5yZ+sqAixHJ+TqNxWjGA6L+8zR1kww+OyT7Irh+9400VuQwLtLaswIDAQAB", "manifest_version": 3, "name": "Microsoft Edge DevTools Enhancements", "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "113.0.1765.0"}, "path": "kfbdpdaobnofkbopebjglnaadopfikhh\\113.0.1765.0_0", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate", "fileSystem.readFullPath", "errorReporting", "edgeLearningToolsPrivate", "fileSystem.getCurrentEntry", "edgePdfPrivate", "edgeCertVerifierPrivate"], "explicit_host": ["edge://resources/*", "edge://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:; trusted-types edge-internal fast-html pdf-url edge-pdf-static-policy;", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "edge_pdf/index.html", "name": "Microsoft Edge PDF Viewer", "offline_enabled": true, "permissions": ["errorReporting", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "edgeCertVerifierPrivate", "edgeLearningToolsPrivate", "edgePdfPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentEntry"]}], "version": "1"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\137.0.3296.83\\resources\\edge_pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "fsd": {"retention_policy_last_version": 137}, "intl": {"selected_languages": "zh-CN,en,en-GB,en-US"}, "language_dwell_time_average": {"zh-CN": 31.88888888888889}, "language_model_counters": {"zh-CN": 9}, "language_usage_count": {"zh-CN": 9}, "media": {"engagement": {"schema_version": 5}}, "muid": {"last_sync": "13394864230011119", "values_seen": []}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "personalization_data_consent": {"personalization_in_context_consent_can_prompt": true, "personalization_in_context_count": 0}, "privacy_sandbox": {"first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 20, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "clear_browsing_data_cookies_exceptions": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"http://localhost,*": {"last_modified": "13394875194416544", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "edge_ad_targeting": {}, "edge_ad_targeting_data": {}, "edge_sdsm": {}, "edge_split_screen": {}, "edge_u2f_api_request": {}, "edge_user_agent_token": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"http://localhost:6100,*": {"expiration": "13402651299635097", "last_modified": "13394875299635100", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 17}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "secure_network": {}, "secure_network_sites": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"http://localhost:6100,*": {"last_modified": "13394875264164331", "setting": {"lastEngagementTime": 1.3394875264164314e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 15.0}}}, "sleeping_tabs": {}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "tech_scam_detection": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "trackers": {}, "trackers_data": {"https://msedgedevtools.microsoft.com:443,*": {"last_modified": "13394864513065342", "setting": {"count": 1}}}, "tracking_org_exceptions": {}, "tracking_org_relationships": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "137.0.3296.83", "creation_time": "13394864229973137", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "edge_password_is_using_new_login_db_path": false, "edge_password_login_db_path_flip_flop_count": 0, "edge_profile_id": "08918086-a3d2-46fa-ba66-01187b9f8d86", "exit_type": "Normal", "has_seen_signin_fre": false, "is_relative_to_aad": true, "last_engagement_time": "13394875264164314", "last_time_obsolete_http_credentials_removed": 1750390690.057049, "last_time_password_store_metrics_reported": **********.06142, "managed_user_id": "", "name": "用户配置 1", "network_pbs": {}, "observed_session_time": {"feedback_rating_in_product_help_observed_session_time_key_137.0.3296.83": 2215.0}, "password_hash_data_list": [], "signin_fre_seen_time": "*****************", "were_old_google_logins_removed": true}, "protection": {"macs": {"browser": {"show_home_button": "751DDCAD9251C2ADEC4B20E73348735F451C5ED5EC9CA386ACEB54CC5615261A"}, "default_search_provider_data": {"template_url_data": "0C0BA9CE3BC2668450FDE87A705B0987B284A4698EB1207EE48C598297A28A0E"}, "edge": {"services": {"account_id": "B17D19C36F4C788FBF1C99AB5F02DD29D96C0DE03459AE02A3609A2D0C6BD7E3", "last_username": "E78F833963CF8225A4190E408960E1D817528D665D7C9E5FC3078D5BDE5080A8"}}, "enterprise_signin": {"policy_recovery_token": "B9842C6C2DA21DD0AD38CF4831CF5C45F0938697BFAAF62DFA9A9D743BB696A8"}, "extensions": {"settings": {"cgjgjfacjflmgphhhepmbhhbgjieaecn": "899F3FD20242BFADBC3C2489276B264046A2B1DD8758B67395923328302200C1", "dgiklkfkllikcanfonkcabmbdfmgleag": "152C5CFF94BF8858259C938B9EDE341D1367AF2BC71AB2D5EF3F4832C33517AD", "kfbdpdaobnofkbopebjglnaadopfikhh": "B05D558889D0F228C096C8DA8B38F976913420DE6B267421119E71DE20F5F9E3", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "6A44E016C4A7CD54C902A9488A09B21242A5705B097791E5D1D80590AD3DC499"}, "ui": {"developer_mode": "8E7D68750FFC6814895D9C69353B3DF59799F38BC0C6A8C78FA8A884F6894D9D"}}, "google": {"services": {"last_signed_in_username": "7466E62FC4AB008C70D4ED927908553B155E2CA9793DF984D553135D2FCA4C7A"}}, "homepage": "8415B205C36DF7C1507344109EC68028894188688834391F109CD53086B71E06", "homepage_is_newtabpage": "59E7E6F524BDBF3C61B0AA22A186E1721E9DEC308B30CF8D01B58961E8ABA81B", "media": {"cdm": {"origin_data": "8295FC78C4ACF1C9836E0FCBD0C9BDCC35181735602001D0226AD1F291834B44"}, "storage_id_salt": "1C9D41143E51A1F8EAD7D49B909750D8135FC5B907FC7EC7DBD4B8311A1F0250"}, "pinned_tabs": "48D2BE73F85281B0B0DE5F41E41B89932CB6FF98E171E5B4ED8BF5B6AB6D5821", "prefs": {"preference_reset_time": "9CDDCAA162003ED21606F92666446C7599A2DF1BFA3E2B79FED490FC8DD4ADA6"}, "safebrowsing": {"incidents_sent": "EF40BAB7FD8FCB3A4BD4AD179BDE5901060EA6F65BAB89024D96F7D1FE9467D0"}, "search_provider_overrides": "9B560C0C3DF1624CF4CF34718843C494CB7495FA1FBD5CE967440D1A324B9FA9", "session": {"restore_on_startup": "B0BD96F394F6F611F8A845934B2FECD3FF1968A9D5CFF59B3A34988E9DA3588D", "startup_urls": "FA695F6DC184B207E007B8AF59338FAFABD249A9282628818AE9B60427A47C86"}}}, "reset_prepopulated_engines": false, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "sessions": {"event_log": [{"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "13394868664979382", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394869538429759", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13394869550346703", "type": 2, "window_count": 0}, {"crashed": false, "time": "13394872022817057", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "13394872334520087", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394872426812261", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "13394872820196895", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394872857333336", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "13394874225431234", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394874233594660", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "13394874329240120", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394874571660711", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "13394874998252349", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394875026052166", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13394875027888921", "type": 2, "window_count": 0}, {"crashed": false, "time": "13394875032411852", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "*****************", "type": 2, "window_count": 0}, {"crashed": false, "time": "*****************", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "*****************", "type": 2, "window_count": 1}], "session_data_status": 3}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["zh-CN"], "dictionary": "", "use_spelling_service": false}, "sync": {"passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "user_experience_metrics": {"personalization_data_consent_enabled_last_known_value": false}}