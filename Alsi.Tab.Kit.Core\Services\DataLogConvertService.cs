﻿using Alsi.Common.Log;
using Alsi.Common.Log.Blf;
using Alsi.Tab.Kit.Core.Models;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Alsi.Tab.Kit.Core.Services
{
    public class DataLogConvertService
    {
        private static readonly ConcurrentDictionary<Guid, ProcessProgress> _progressCache = new ConcurrentDictionary<Guid, ProcessProgress>();
        private static readonly ConcurrentDictionary<Guid, CancellationTokenSource> _cancellationTokens = new ConcurrentDictionary<Guid, CancellationTokenSource>();

        public void ReadWriteAsc(string ascPath)
        {
            var frames = LogReader.Read(ascPath);
            var ascLogWriter = new LogWriter();
            foreach (var frame in frames)
            {
                ascLogWriter.Log(frame);
            }
            ascLogWriter.Release();
        }

        public void ReadWriteBlf(string blfPath)
        {
            var frames = BlfLogReader.ReadBlfFile(blfPath);

            var ascLogWriter = new BlfLogWriter();
            ascLogWriter.Log(frames);
            ascLogWriter.Release();
        }

        private static string GetResultFolder(string sourceFilePath)
        {
            var sourceFolder = Directory.GetParent(sourceFilePath).FullName;
            var resultFolder = Path.Combine(sourceFolder, $"tabkit_log_convert_{DateTime.Now:yyyyMMdd_HHmmss}");
            return resultFolder;
        }

        public static FileProgress[] CalcFileProgresses(DataLogProcessRequest request)
        {
            var resultFolder = GetResultFolder(request.SourceFilePath);
            var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(request.SourceFilePath);
            var extension = request.TargetFormat.ToString().ToLower();
            var sourceFileSize = new FileInfo(request.SourceFilePath).Length;

            var fileProgresses = new List<FileProgress>();
            if (request.EnableSplit && request.SplitFileCount > 1)
            {
                var avgFileSize = sourceFileSize / request.SplitFileCount;
                var remainFileSize = sourceFileSize;
                for (int i = 0; i < request.SplitFileCount; i++)
                {
                    var fileName = $"{fileNameWithoutExtension}_{i + 1:D3}{extension}";
                    var filePath = Path.Combine(resultFolder, fileName);

                    var fileSize = Math.Min(avgFileSize, remainFileSize);

                    var fileProgress = new FileProgress
                    {
                        FileName = fileName,
                        FilePath = filePath,
                        Status = ProcessStatus.Pending,
                        ProgressPercentage = 0,
                        FileSize = fileSize,
                        ErrorMessage = string.Empty
                    };

                    fileProgresses.Add(fileProgress);
                    remainFileSize -= fileSize;
                }
            }
            else
            {
                var fileName = $"{fileNameWithoutExtension}{extension}";
                var path = Path.Combine(resultFolder, $"{fileNameWithoutExtension}{extension}");
                var fileProgress = new FileProgress
                {
                    FileName = fileName,
                    FilePath = path,
                    Status = ProcessStatus.Pending,
                    ProgressPercentage = 0,
                    FileSize = sourceFileSize,
                    ErrorMessage = string.Empty
                };
                fileProgresses.Add(fileProgress);
            }
            return fileProgresses.ToArray();
        }

        public Guid StartProcessAsync(DataLogProcessRequest request)
        {
            var taskId = Guid.NewGuid();
            var cancellationTokenSource = new CancellationTokenSource();
            _cancellationTokens[taskId] = cancellationTokenSource;

            // 初始化进度信息
            var progress = new ProcessProgress
            {
                TaskId = taskId,
                OverallProgressPercentage = 0,
                CurrentOperation = "准备处理...",
                FileProgresses = Array.Empty<FileProgress>(),
                IsCompleted = false
            };
            _progressCache[taskId] = progress;

            // 异步执行处理
            Task.Run(async () => await ProcessDataLogInternalAsync(request, taskId, cancellationTokenSource.Token));

            return progress.TaskId;
        }

        public ProcessProgress GetProgress(Guid taskId)
        {
            _progressCache.TryGetValue(taskId, out var progress);
            return progress; // 如果没找到会返回null，这是正常的
        }

        public bool CancelProcess(Guid taskId)
        {
            if (_cancellationTokens.TryGetValue(taskId, out var cancellationTokenSource))
            {
                cancellationTokenSource.Cancel();
                return true;
            }
            return false;
        }

        private async Task ProcessDataLogInternalAsync(DataLogProcessRequest request, Guid taskId, CancellationToken cancellationToken)
        {
            var progress = _progressCache[taskId];

            try
            {
                progress.CurrentOperation = "分析源文件...";

                // 初始化每个文件的进度
                progress.FileProgresses = CalcFileProgresses(request);
                progress.CurrentOperation = "开始处理文件...";

                // 执行实际的文件处理
                await ProcessFilesAsync(request, progress, cancellationToken);

                progress.IsCompleted = true;
                progress.OverallProgressPercentage = 100;
                progress.CurrentOperation = "处理完成";
            }
            catch (OperationCanceledException)
            {
                progress.CurrentOperation = "处理已取消";
                foreach (var fileProgress in progress.FileProgresses)
                {
                    if (fileProgress.Status == ProcessStatus.Processing)
                    {
                        fileProgress.Status = ProcessStatus.Cancelled;
                    }
                }
            }
            catch (Exception ex)
            {
                progress.ErrorMessage = ex.Message;
                progress.CurrentOperation = "处理失败";
                foreach (var fileProgress in progress.FileProgresses)
                {
                    if (fileProgress.Status == ProcessStatus.Processing)
                    {
                        fileProgress.Status = ProcessStatus.Failed;
                        fileProgress.ErrorMessage = ex.Message;
                    }
                }
            }
            finally
            {
                _cancellationTokens.TryRemove(taskId, out _);
            }
        }

        private DataLogFormat DetectFileFormat(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            switch (extension)
            {
                case ".asc":
                    return DataLogFormat.Asc;
                case ".blf":
                    return DataLogFormat.Blf;
                default:
                    return DataLogFormat.Unknown;
            }
        }

        private Task ProcessFilesAsync(DataLogProcessRequest request, ProcessProgress progress, CancellationToken cancellationToken)
        {
            var sourcePath = request.SourceFilePath;
            var frames = Array.Empty<IFrame>();

            Action<string, IFrame[], Action<int>> writeLogFunc = null;
            if (request.TargetFormat == DataLogFormat.Asc)
            {
                frames = LogReader.Read(sourcePath);
                writeLogFunc = WriteAsc;
            }
            else if (request.TargetFormat == DataLogFormat.Blf)
            {
                frames = BlfLogReader.ReadBlfFile(sourcePath);
                writeLogFunc = WriteBlf;
            }
            else
            {
                throw new Exception($"Unknown file format: {sourcePath}");
            }

            var totalFrameCount = frames.Length;
            var sourceFileSize = new FileInfo(request.SourceFilePath).Length;
            var handledCount = 0;
            for (int j = 0; j < progress.FileProgresses.Length; j++)
            {
                FileProgress fileProgress = progress.FileProgresses[j];
                cancellationToken.ThrowIfCancellationRequested();

                fileProgress.Status = ProcessStatus.Processing;

                var currentCount = (int)(fileProgress.FileSize / sourceFileSize * totalFrameCount);
                if (j == progress.FileProgresses.Length - 1)
                {
                    currentCount = totalFrameCount - handledCount;
                }

                var currentFrames = frames.Skip(handledCount).Take(currentCount).ToArray();
                writeLogFunc(fileProgress.FilePath,
                    currentFrames,
                    (progressValue) =>
                    {
                        fileProgress.ProgressPercentage = progressValue;
                        progress.OverallProgressPercentage = CalculateOverallProgress(progress.FileProgresses);
                    });

                fileProgress.Status = ProcessStatus.Completed;
            }

            return Task.CompletedTask;
        }

        private int CalculateOverallProgress(FileProgress[] fileProgresses)
        {
            if (fileProgresses.Length == 0)
            {
                return 0;
            }
            return (int)fileProgresses.Average(f => f.ProgressPercentage);
        }

        public static void WriteAsc(string path, IFrame[] frames, Action<int> onProgressChanged)
        {
            var prevProgress = 0;
            var ascLogWriter = new LogWriter();
            ascLogWriter.Initialize(path);
            for (var i = 0; i < frames.Length; i++)
            {
                var progress = i / frames.Length;
                if (prevProgress != progress)
                {
                    onProgressChanged?.Invoke(progress);
                    prevProgress = progress;
                }
                ascLogWriter.Log(frames[i]);
            }

            ascLogWriter.Release();
        }

        private static void WriteBlf(string path, IFrame[] frames, Action<int> onProgressChanged)
        {
            var prevProgress = 0;
            var blfLogWriter = new BlfLogWriter();
            blfLogWriter.Initialize(path);
            for (var i = 0; i < frames.Length; i++)
            {
                var progress = i / frames.Length;
                if (prevProgress != progress)
                {
                    onProgressChanged?.Invoke(progress);
                    prevProgress = progress;
                }
                BlfLogWriter.ToLog(frames[i]);
            }
            blfLogWriter.Release();

            onProgressChanged?.Invoke(100);
        }
    }
}