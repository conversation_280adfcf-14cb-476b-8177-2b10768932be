{"Version": 1, "WorkspaceRootPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{32F4AFD7-A5C8-4D09-A996-C849891054DC}|Alsi.Tab.Kit\\Alsi.Tab.Kit.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.tab.kit\\alsi.tab.kit\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{32F4AFD7-A5C8-4D09-A996-C849891054DC}|Alsi.Tab.Kit\\Alsi.Tab.Kit.csproj|solutionrelative:alsi.tab.kit\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.tab.kit\\alsi.tab.kit.core\\services\\datalogconvertservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|solutionrelative:alsi.tab.kit.core\\services\\datalogconvertservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47A11109-4F55-41CE-9BD3-C74687D7A212}|..\\Alsi.Common\\Alsi.App.Desktop\\Alsi.App.Desktop.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.common\\alsi.app.desktop\\utils\\uiutils.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.tab.kit\\alsi.tab.kit.core\\models\\datalogmodels.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|solutionrelative:alsi.tab.kit.core\\models\\datalogmodels.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{6324226f-61b6-4f28-92ee-18d4b5fe1e48}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "App.xaml.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit\\App.xaml.cs", "RelativeDocumentMoniker": "Alsi.Tab.Kit\\App.xaml.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit\\App.xaml.cs", "RelativeToolTip": "Alsi.Tab.Kit\\App.xaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T06:36:47.632Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "UiUtils.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Desktop\\Utils\\UiUtils.cs", "RelativeDocumentMoniker": "..\\Alsi.Common\\Alsi.App.Desktop\\Utils\\UiUtils.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Desktop\\Utils\\UiUtils.cs", "RelativeToolTip": "..\\Alsi.Common\\Alsi.App.Desktop\\Utils\\UiUtils.cs", "ViewState": "AgIAADYAAAAAAAAAAADwvwgAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T06:26:49.717Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "DataLogConvertService.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\DataLogConvertService.cs", "RelativeDocumentMoniker": "Alsi.Tab.Kit.Core\\Services\\DataLogConvertService.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\DataLogConvertService.cs", "RelativeToolTip": "Alsi.Tab.Kit.Core\\Services\\DataLogConvertService.cs", "ViewState": "AgIAAOcAAAAAAAAAAAAgwPgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T06:04:37.473Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "DataLogModels.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Models\\DataLogModels.cs", "RelativeDocumentMoniker": "Alsi.Tab.Kit.Core\\Models\\DataLogModels.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Models\\DataLogModels.cs", "RelativeToolTip": "Alsi.Tab.Kit.Core\\Models\\DataLogModels.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T05:17:16.02Z", "EditorCaption": ""}]}]}]}