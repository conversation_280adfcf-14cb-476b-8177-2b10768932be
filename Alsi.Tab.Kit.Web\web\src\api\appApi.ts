import axios, { AxiosResponse } from 'axios';

// 定义错误数据结构
export interface ErrorData {
  message: string;
  stack?: string;
  url: string;
  type?: string;
  vueHookInfo?: string; 
  codeInfo?: string;
}

// 定义应用信息接口
export interface AppInfo {
  dataFolder: string;
  logFolder: string;
}

// 定义测试模型接口
export interface TestMode {
  name: string;
}

// 数据日志转换相关接口
export enum DataLogFormat {
  Unknown = 0,
  Asc = 1,
  Blf = 2
}

export interface DataLogFileInfo {
  filePath: string;
  format: DataLogFormat;
  fileSizeBytes: number;
  estimatedFrameCount: number;
  isValid: boolean;
  errorMessage?: string;
}

export interface DataLogProcessRequest {
  sourceFilePath: string;
  targetFormat: DataLogFormat;
  enableSplit: boolean;
  splitFileCount: number;
}

export interface EstimatedOutputFile {
  fileName: string;
  estimatedSizeBytes: number;
}

export interface ProcessPreview {
  sourceFileInfo: DataLogFileInfo;
  targetFormat: DataLogFormat;
  estimatedOutputFileCount: number;
  estimatedTotalSizeBytes: number;
  estimatedFiles: EstimatedOutputFile[];
}

export interface OutputFileInfo {
  filePath: string;
  fileName: string;
  fileSizeBytes: number;
  frameCount: number;
  status: ProcessStatus;
  progressPercentage: number;
  errorMessage?: string;
}

export interface DataLogProcessResult {
  success: boolean;
  message?: string;
  taskId: string;
  outputFiles: OutputFileInfo[];
  processedFrameCount: number;
  startTime: string;
  endTime?: string;
}

export interface FileProgress {
  fileName: string;
  filePath: string;
  status: ProcessStatus;
  progressPercentage: number;
  fileSize: number;
  errorMessage?: string;
}

export interface ProcessProgress {
  taskId: string;
  overallProgressPercentage: number;
  currentOperation: string;
  isCompleted: boolean;
  errorMessage?: string;
  fileProgresses: FileProgress[];
}

export enum ProcessStatus {
  Pending = 0,
  Processing = 1,
  Completed = 2,
  Failed = 3,
  Cancelled = 4
}

export interface FileSelectionResult {
  success: boolean;
  filePath?: string;
  errorMessage?: string;
  userCancelled?: boolean;
}

export interface FolderSelectionResult {
  success: boolean;
  folderPath?: string;
  errorMessage?: string;
  userCancelled?: boolean;
}

const BASE_URL = '/api/app'
const DATALOG_BASE_URL = '/api/DataLogConvert'

export const appApi = {
  // 获取应用信息
  getAppInfo(): Promise<AxiosResponse<AppInfo>> {
    return axios.get(`${BASE_URL}/appInfo`);
  },

  // 记录错误日志
  logError: (errorData: ErrorData) => {
    return axios.post(`${BASE_URL}/logError`, errorData);
  },

  // 退出应用程序
  exit: () => {
    return axios.post(`${BASE_URL}/exit`);
  },

  // 获取测试模型
  getTestModel(): Promise<AxiosResponse<TestMode>> {
    return axios.get(`api/test/model`);
  },

  // 数据日志转换相关接口
  dataLogConvert: {
    // 选择文件
    selectFile(): Promise<AxiosResponse<string>> {
      return axios.post(`${DATALOG_BASE_URL}/select-file`);
    },

    // 预览处理
    previewProcess(request: DataLogProcessRequest): Promise<AxiosResponse<FileProgress[]>> {
      return axios.post(`${DATALOG_BASE_URL}/preview`, request);
    },

    // 开始处理
    startProcess(request: DataLogProcessRequest): Promise<AxiosResponse<string>> {
      return axios.post(`${DATALOG_BASE_URL}/start`, request);
    },

    // 获取进度
    getProgress(taskId: string): Promise<AxiosResponse<ProcessProgress>> {
      return axios.get(`${DATALOG_BASE_URL}/progress?taskId=${taskId}`);
    },

    // 取消处理
    cancelProcess(taskId: string): Promise<AxiosResponse<{ success: boolean }>> {
      return axios.post(`${DATALOG_BASE_URL}/cancel`, null, { params: { taskId } });
    }
  }
}

export default appApi
