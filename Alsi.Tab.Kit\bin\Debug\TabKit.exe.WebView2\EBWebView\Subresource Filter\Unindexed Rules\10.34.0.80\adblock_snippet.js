(()=>{function e(){"undefined"!=typeof videoAdsBlockerNativeHandler&&videoAdsBlockerNativeHandler.logBlockSuccess()}function t(t,n){if(!t)throw new Error("[override-property-read snippet]: No property to override.");if(void 0===n)throw new Error("[override-property-read snippet]: No value to override with.");let l;if("false"===n)l=!1;else if("true"===n)l=!0;else if("null"===n)l=null;else if("noopFunc"===n)l=()=>{};else if("trueFunc"===n)l=()=>!0;else if("falseFunc"===n)l=()=>!1;else if(/^\d+$/.test(n))l=parseFloat(n);else if(""===n)l=n;else if("undefined"!==n)throw new Error(`[override-property-read snippet]: Value "${n}" is not valid.`);r(window,t,{get:()=>(e(),l),set(){}})}function r(e,t,n){let l=t.indexOf(".");if(-1==l){let r=Object.getOwnPropertyDescriptor(e,t);if(r&&!r.configurable)return;let l=Object.assign({},n,{configurable:!0});if(!r&&!l.get&&l.set){let r=e[t];l.get=()=>r}return void Object.defineProperty(e,t,l)}let o=t.slice(0,l);t=t.slice(l+1);let s=e[o];!s||"object"!=typeof s&&"function"!=typeof s||r(s,t,n);let i=Object.getOwnPropertyDescriptor(e,o);i&&!i.configurable||Object.defineProperty(e,o,{get:()=>s,set:e=>{s=e,!e||"object"!=typeof e&&"function"!=typeof s||r(e,t,n)},configurable:!0})}let n={isOwnProperty:Object.prototype.hasOwnProperty};t("playerResponse.adPlacements","undefined"),t("ytInitialPlayerResponse.adPlacements","undefined"),function(t,r=""){if(!t)throw new Error("Missing paths to prune");let l=t.split(/ +/),o=""!==r?r.split(/ +/):[],s=JSON.parse,i={value(...t){let r;if(r=s.apply(this,t),o.length>0&&o.some((e=>!p(r,e))))return r;for(let t of l){let n=p(r,t);void 0!==n&&(e(),delete n[0][n[1]])}return r}};function p(e,t){if(!(e instanceof window.Object))return;let r=e,l=t.split(".");if(0===l.length)return;for(let e=0;e<l.length-1;e++){let t=l[e];if(!n.isOwnProperty.call(r,t))return;if(r=r[t],!(r instanceof window.Object))return}let o=l[l.length-1];return n.isOwnProperty.call(r,o)?[r,o]:void 0}Object.defineProperty(JSON,"parse",i)}("0.playerResponse.adPlacements 0.playerResponse.playerAds 1.playerResponse.adPlacements 1.playerResponse.playerAds 2.playerResponse.adPlacements 2.playerResponse.playerAds playerResponse.adPlacements playerResponse.playerAds ytInitialPlayerResponse.adPlacements ytInitialPlayerResponse.playerAds adPlacements playerAds adSlots")})();