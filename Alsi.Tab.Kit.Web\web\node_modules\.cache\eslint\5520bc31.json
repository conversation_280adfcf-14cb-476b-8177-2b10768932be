[{"D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\main.ts": "1", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\api\\appApi.ts": "2", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\utils\\errorHandler.ts": "3", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\App.vue": "4", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\router\\index.ts": "5", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\store\\index.ts": "6", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\HomeView.vue": "7", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\AboutView.vue": "8", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\LogConverterView.vue": "9"}, {"size": 3949, "mtime": 1750325656295, "results": "10", "hashOfConfig": "11"}, {"size": 2617, "mtime": 1750398658283, "results": "12", "hashOfConfig": "11"}, {"size": 2910, "mtime": 1750325656311, "results": "13", "hashOfConfig": "11"}, {"size": 5435, "mtime": 1750398308051, "results": "14", "hashOfConfig": "11"}, {"size": 667, "mtime": 1750325656308, "results": "15", "hashOfConfig": "11"}, {"size": 145, "mtime": 1750325656297, "results": "16", "hashOfConfig": "11"}, {"size": 3211, "mtime": 1750401868461, "results": "17", "hashOfConfig": "11"}, {"size": 5299, "mtime": 1750401736949, "results": "18", "hashOfConfig": "11"}, {"size": 10460, "mtime": 1750401391042, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "22"}, "abycao", {"filePath": "23", "messages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "27", "usedDeprecatedRules": "22"}, {"filePath": "28", "messages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "22"}, {"filePath": "32", "messages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "22"}, {"filePath": "34", "messages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\main.ts", [], [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\api\\appApi.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\utils\\errorHandler.ts", ["40", "41", "42"], "import axios from 'axios';\nimport { ElMessage, ElMessageBox } from 'element-plus';\n\n// 格式化错误信息，显示所有层级的异常\nconst formatErrorMessage = (error: any): string => {\n  if (!error.response || !error.response.data) {\n    return error.message || 'Unknown error';\n  }\n\n  const errorData = error.response.data;\n  const errorMessages = [];\n\n  // 添加主异常信息\n  if (errorData.exceptionMessage) {\n    errorMessages.push(errorData.exceptionMessage);\n  }\n\n  // 递归添加所有内部异常信息\n  let currentException = errorData.innerException;\n  while (currentException) {\n    if (currentException.exceptionMessage) {\n      errorMessages.push(currentException.exceptionMessage);\n    }\n    currentException = currentException.innerException;\n  }\n\n  // 如果没有找到任何异常信息，返回通用错误消息\n  if (errorMessages.length === 0) {\n    return errorData.message || 'An error occurred';\n  }\n\n  // 返回所有异常信息，每个一行\n  return errorMessages.join('<br>');\n};\n\n// 显示详细错误信息\nconst showDetailedError = (error: any): void => {\n  if (!error.response || !error.response.data) {\n    ElMessage.error(error.message || 'Unknown error');\n    return;\n  }\n\n  // 获取格式化的错误信息\n  const errorMessage = formatErrorMessage(error);\n\n  // 使用对话框显示详细错误信息\n  ElMessageBox.alert(\n    errorMessage,\n    'Error',\n    {\n      confirmButtonText: 'OK',\n      dangerouslyUseHTMLString: true,\n      closeOnClickModal: true,  // 允许点击空白区域关闭\n      closeOnPressEscape: true, // 允许按ESC键关闭\n      showClose: true           // 显示右上角关闭按钮\n    }\n  );\n};\n\n// 检查是否为用户取消操作\nconst isUserCanceled = (error: any): boolean => {\n  // 检查错误响应数据\n  if (error.response && error.response.data) {\n    // 检查直接等于字符串的情况\n    if (error.response.data === 'UserCanceled') {\n      return true;\n    }\n\n    // 检查错误消息字段\n    if (error.response.data.message === 'UserCanceled') {\n      return true;\n    }\n\n    // 检查错误代码字段\n    if (error.response.data.errorCode === 'UserCanceled') {\n      return true;\n    }\n  }\n\n  return false;\n};\n\n// 设置响应拦截器\nexport const setupErrorHandler = (): void => {\n  axios.interceptors.response.use(\n    response => response,\n    error => {\n      // 检查是否为用户取消操作\n      if (isUserCanceled(error)) {\n        // 用户取消操作，显示信息提示而不是错误\n        ElMessage.info(\"Operation cancelled by user\");\n\n        // 继续抛出错误，以便调用者可以进行额外处理\n        return Promise.reject(error);\n      }\n\n      // 处理其他错误\n      showDetailedError(error);\n\n      // 继续抛出错误，以便调用者可以进行额外处理\n      return Promise.reject(error);\n    }\n  );\n};\n\nexport default setupErrorHandler;\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\App.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\router\\index.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\store\\index.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\HomeView.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\AboutView.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\LogConverterView.vue", ["43"], {"ruleId": "44", "severity": 1, "message": "45", "line": 5, "column": 36, "nodeType": "46", "messageId": "47", "endLine": 5, "endColumn": 39, "suggestions": "48"}, {"ruleId": "44", "severity": 1, "message": "45", "line": 37, "column": 35, "nodeType": "46", "messageId": "47", "endLine": 37, "endColumn": 38, "suggestions": "49"}, {"ruleId": "44", "severity": 1, "message": "45", "line": 61, "column": 32, "nodeType": "46", "messageId": "47", "endLine": 61, "endColumn": 35, "suggestions": "50"}, {"ruleId": "51", "severity": 1, "message": "52", "line": 168, "column": 68, "nodeType": "53", "messageId": "54", "endLine": 168, "endColumn": 88}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["55", "56"], ["57", "58"], ["59", "60"], "@typescript-eslint/no-non-null-assertion", "Forbidden non-null assertion.", "TSNonNullExpression", "noNonNull", {"messageId": "61", "fix": "62", "desc": "63"}, {"messageId": "64", "fix": "65", "desc": "66"}, {"messageId": "61", "fix": "67", "desc": "63"}, {"messageId": "64", "fix": "68", "desc": "66"}, {"messageId": "61", "fix": "69", "desc": "63"}, {"messageId": "64", "fix": "70", "desc": "66"}, "suggestUnknown", {"range": "71", "text": "72"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "71", "text": "73"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "74", "text": "72"}, {"range": "74", "text": "73"}, {"range": "75", "text": "72"}, {"range": "75", "text": "73"}, [140, 143], "unknown", "never", [931, 934], [1479, 1482]]