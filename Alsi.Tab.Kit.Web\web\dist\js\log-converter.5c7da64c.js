"use strict";(self["webpackChunktab_kit_web"]=self["webpackChunktab_kit_web"]||[]).push([[488],{6425:function(e,l,a){a.r(l),a.d(l,{default:function(){return M}});a(8111),a(8237);var s=a(6768),t=a(4232);const r={class:"log-converter"},o={class:"converter-content"},i={class:"split-controls"},n={key:0,class:"split-count-label"},u={key:0,class:"preview-section"},c={class:"preview-summary"},p={class:"summary-item"},d={class:"value"},v={class:"summary-item"},g={class:"value"},f={class:"output-files"},k={class:"file-list"},m={class:"file-name"},h={class:"file-size"},F={class:"action-buttons"},b={key:1,class:"progress-section"},C={class:"current-operation"},P={key:0,class:"file-progress"},L={class:"file-progress-list"},_={class:"file-name"},y={class:"file-status"},w={class:"status-bar"},K={key:0},S={key:1},R={key:2,class:"processing-status"};function Q(e,l,a,Q,V,X){const E=(0,s.g2)("el-button"),z=(0,s.g2)("el-input"),B=(0,s.g2)("el-form-item"),M=(0,s.g2)("el-radio"),q=(0,s.g2)("el-radio-group"),G=(0,s.g2)("el-switch"),W=(0,s.g2)("el-input-number"),I=(0,s.g2)("el-form"),N=(0,s.g2)("el-progress");return(0,s.uX)(),(0,s.CE)("div",r,[l[15]||(l[15]=(0,s.Lk)("div",{class:"title-bar"},"Log 转换工具",-1)),(0,s.Lk)("div",o,[(0,s.bF)(I,{model:e.fileForm,"label-width":"120px","label-position":"top"},{default:(0,s.k6)(()=>[(0,s.bF)(B,{label:"Log 文件路径:"},{default:(0,s.k6)(()=>[(0,s.bF)(z,{modelValue:e.fileForm.filePath,"onUpdate:modelValue":l[0]||(l[0]=l=>e.fileForm.filePath=l),placeholder:"请输入或选择一个 Log 文件，支持格式：.asc .blf",onBlur:e.analyzeFile},{append:(0,s.k6)(()=>[(0,s.bF)(E,{onClick:e.selectFile},{default:(0,s.k6)(()=>l[4]||(l[4]=[(0,s.eW)(" 选择 Log 文件 ")])),_:1,__:[4]},8,["onClick"])]),_:1},8,["modelValue","onBlur"])]),_:1}),(0,s.bF)(B,{label:"目标格式:"},{default:(0,s.k6)(()=>[(0,s.bF)(q,{modelValue:e.processRequest.targetFormat,"onUpdate:modelValue":l[1]||(l[1]=l=>e.processRequest.targetFormat=l)},{default:(0,s.k6)(()=>[(0,s.bF)(M,{value:1},{default:(0,s.k6)(()=>l[5]||(l[5]=[(0,s.eW)("ASC")])),_:1,__:[5]}),(0,s.bF)(M,{value:2},{default:(0,s.k6)(()=>l[6]||(l[6]=[(0,s.eW)("BLF")])),_:1,__:[6]})]),_:1},8,["modelValue"])]),_:1}),(0,s.bF)(B,{label:"启用分割:"},{default:(0,s.k6)(()=>[(0,s.Lk)("div",i,[(0,s.bF)(G,{modelValue:e.processRequest.enableSplit,"onUpdate:modelValue":l[2]||(l[2]=l=>e.processRequest.enableSplit=l),onChange:e.onSplitToggle},null,8,["modelValue","onChange"]),e.processRequest.enableSplit?((0,s.uX)(),(0,s.CE)("span",n,"分割文件数:")):(0,s.Q3)("",!0),e.processRequest.enableSplit?((0,s.uX)(),(0,s.Wv)(W,{key:1,modelValue:e.splitFileCount,"onUpdate:modelValue":l[3]||(l[3]=l=>e.splitFileCount=l),min:1,max:100,onChange:e.onSplitCountChange,class:"split-count-input"},null,8,["modelValue","onChange"])):(0,s.Q3)("",!0)])]),_:1})]),_:1},8,["model"]),e.preview&&e.preview.length>0?((0,s.uX)(),(0,s.CE)("div",u,[l[10]||(l[10]=(0,s.Lk)("h4",null,"转换预览",-1)),(0,s.Lk)("div",c,[(0,s.Lk)("div",p,[l[7]||(l[7]=(0,s.Lk)("span",{class:"label"},"输出文件数:",-1)),(0,s.Lk)("span",d,(0,t.v_)(e.preview.length),1)]),(0,s.Lk)("div",v,[l[8]||(l[8]=(0,s.Lk)("span",{class:"label"},"总大小:",-1)),(0,s.Lk)("span",g,(0,t.v_)(e.formatFileSize(e.preview.reduce((e,l)=>e+l.fileSize,0))),1)])]),(0,s.Lk)("div",f,[l[9]||(l[9]=(0,s.Lk)("h5",null,"输出文件列表",-1)),(0,s.Lk)("div",k,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(e.preview,(l,a)=>((0,s.uX)(),(0,s.CE)("div",{key:a,class:"file-item"},[(0,s.Lk)("div",m,(0,t.v_)(l.fileName),1),(0,s.Lk)("div",h,(0,t.v_)(e.formatFileSize(l.fileSize)),1)]))),128))])])])):(0,s.Q3)("",!0),(0,s.Lk)("div",F,[(0,s.bF)(E,{type:"primary",disabled:!e.preview||0===e.preview.length,onClick:e.startProcess,loading:e.isProcessing},{default:(0,s.k6)(()=>l[11]||(l[11]=[(0,s.eW)(" 开始转换 ")])),_:1,__:[11]},8,["disabled","onClick","loading"]),(0,s.bF)(E,{type:"danger",disabled:!e.isProcessing,onClick:e.cancelProcess},{default:(0,s.k6)(()=>l[12]||(l[12]=[(0,s.eW)(" 取消转换 ")])),_:1,__:[12]},8,["disabled","onClick"])]),e.progress?((0,s.uX)(),(0,s.CE)("div",b,[l[14]||(l[14]=(0,s.Lk)("h4",null,"转换进度",-1)),(0,s.bF)(N,{percentage:e.progress.overallProgressPercentage,status:e.progress.isCompleted?"success":"active"},null,8,["percentage","status"]),(0,s.Lk)("p",C,(0,t.v_)(e.progress.currentOperation),1),e.progress.fileProgresses&&e.progress.fileProgresses.length>0?((0,s.uX)(),(0,s.CE)("div",P,[l[13]||(l[13]=(0,s.Lk)("h5",null,"文件处理详情",-1)),(0,s.Lk)("div",L,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(e.progress.fileProgresses,(l,a)=>((0,s.uX)(),(0,s.CE)("div",{key:a,class:"file-progress-item"},[(0,s.Lk)("div",_,(0,t.v_)(l.fileName),1),(0,s.Lk)("div",y,(0,t.v_)(e.getStatusName(l.status)),1),(0,s.bF)(N,{percentage:l.progressPercentage,"show-text":!1,size:"small"},null,8,["percentage"])]))),128))])])):(0,s.Q3)("",!0)])):(0,s.Q3)("",!0)]),(0,s.Lk)("div",w,[e.fileForm.filePath?((0,s.uX)(),(0,s.CE)("span",K," 文件: 已选择 - "+(0,t.v_)(e.fileForm.filePath),1)):(0,s.Q3)("",!0),e.fileForm.filePath?(0,s.Q3)("",!0):((0,s.uX)(),(0,s.CE)("span",S,"请选择 Log 文件")),e.isProcessing?((0,s.uX)(),(0,s.CE)("span",R," 转换中... "+(0,t.v_)(e.progress?.overallProgressPercentage||0)+"% ",1)):(0,s.Q3)("",!0)])])}var V=a(144),X=a(1021),E=(0,s.pM)({name:"LogConverterView",setup(){const e=(0,V.KR)({filePath:""}),l=(0,V.KR)({sourceFilePath:"",targetFormat:X.Mo.Asc,enableSplit:!1,splitFileCount:1}),a=(0,V.KR)(1),t=(0,V.KR)(null),r=(0,V.KR)(null),o=(0,V.KR)(null),i=(0,V.KR)(!1);let n=null;const u=async()=>{const a=await X.GQ.dataLogConvert.selectFile(),s=a.data;s&&(e.value.filePath=s,l.value.sourceFilePath=s,await p())},c=async()=>{if(e.value.filePath)try{l.value.sourceFilePath=e.value.filePath,await p()}catch(a){console.error("文件分析失败:",a)}},p=async()=>{if(l.value.sourceFilePath)try{const e=await X.GQ.dataLogConvert.previewProcess(l.value);t.value=e.data}catch(e){console.error("预览失败:",e)}},d=()=>{l.value.enableSplit&&(a.value=1,l.value.splitFileCount=1),l.value.sourceFilePath&&p()},v=()=>{l.value.splitFileCount=a.value,l.value.sourceFilePath&&p()},g=async()=>{i.value=!0;try{const e=await X.GQ.dataLogConvert.startProcess(l.value);r.value=e.data,f()}catch(e){console.error("开始处理失败:",e),i.value=!1}},f=()=>{r.value&&(n=window.setInterval(async()=>{try{const e=await X.GQ.dataLogConvert.getProgress(r.value);o.value=e.data,o.value?.isCompleted&&(k(),i.value=!1)}catch(e){console.error("获取进度失败:",e)}},1e3))},k=()=>{n&&(clearInterval(n),n=null)},m=async()=>{if(r.value)try{await X.GQ.dataLogConvert.cancelProcess(r.value),k(),i.value=!1}catch(e){console.error("取消处理失败:",e)}},h=e=>{const l=["Bytes","KB","MB","GB"];if(0===e)return"0 Bytes";const a=Math.floor(Math.log(e)/Math.log(1024));return Math.round(e/Math.pow(1024,a)*100)/100+" "+l[a]},F=e=>{switch(e){case X.KK.Pending:return"等待中";case X.KK.Processing:return"处理中";case X.KK.Completed:return"已完成";case X.KK.Failed:return"失败";case X.KK.Cancelled:return"已取消";default:return"未知"}};return(0,s.hi)(()=>{k()}),{fileForm:e,processRequest:l,splitFileCount:a,preview:t,progress:o,isProcessing:i,selectFile:u,analyzeFile:c,onSplitToggle:d,onSplitCountChange:v,startProcess:g,cancelProcess:m,formatFileSize:h,getStatusName:F}}}),z=a(1241);const B=(0,z.A)(E,[["render",Q],["__scopeId","data-v-8c08b2d8"]]);var M=B}}]);
//# sourceMappingURL=log-converter.5c7da64c.js.map