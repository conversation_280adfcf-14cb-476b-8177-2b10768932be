(function(){"use strict";var e={1021:function(e,n,o){o.d(n,{GQ:function(){return c},KK:function(){return r},Mo:function(){return t}});var t,r,s=o(4373);(function(e){e[e["Unknown"]=0]="Unknown",e[e["Asc"]=1]="Asc",e[e["Blf"]=2]="Blf"})(t||(t={})),function(e){e["Pending"]="Pending",e["Processing"]="Processing",e["Completed"]="Completed",e["Failed"]="Failed",e["Cancelled"]="Cancelled"}(r||(r={}));const a="/api/app",i="/api/DataLogConvert",c={getAppInfo(){return s.A.get(`${a}/appInfo`)},logError:e=>s.A.post(`${a}/logError`,e),exit:()=>s.A.post(`${a}/exit`),getTestModel(){return s.A.get("api/test/model")},dataLogConvert:{selectFile(){return s.A.post(`${i}/select-file`)},startProcess(e){return s.A.post(`${i}/start`,e)},getProgress(e){return s.A.get(`${i}/progress?taskId=${e}`)},cancelProcess(e){return s.A.post(`${i}/cancel`,null,{params:{taskId:e}})}}}},1803:function(e,n,o){var t=o(5130),r=o(6768),s=o(4232),a=o(3935);const i={id:"app"},c={class:"menu-header"},l={key:0,class:"logo-container"},u={key:1,class:"logo-container-collapsed"},d={class:"menu-items"},f={key:0,class:"menu-text"},p={key:0,class:"menu-text"},g={class:"menu-bottom"},m={key:0,class:"menu-text"},v={class:"menu-toggle-section"};function h(e,n,o,t,h,k){const b=(0,r.g2)("router-link"),y=(0,r.g2)("font-awesome-icon"),C=(0,r.g2)("router-view");return(0,r.uX)(),(0,r.CE)("div",i,[(0,r.Lk)("div",{class:(0,s.C4)(["sidebar",{collapsed:e.isMenuCollapsed}])},[(0,r.Lk)("div",c,[e.isMenuCollapsed?((0,r.uX)(),(0,r.CE)("div",u,[(0,r.bF)(b,{to:"/"},{default:(0,r.k6)(()=>n[3]||(n[3]=[(0,r.Lk)("img",{src:a,alt:"Logo",class:"logo-small"},null,-1)])),_:1,__:[3]})])):((0,r.uX)(),(0,r.CE)("div",l,[(0,r.bF)(b,{to:"/"},{default:(0,r.k6)(()=>n[1]||(n[1]=[(0,r.Lk)("img",{src:a,alt:"Logo",class:"logo"},null,-1)])),_:1,__:[1]}),n[2]||(n[2]=(0,r.Lk)("span",{class:"app-name"},"TabKit",-1))]))]),(0,r.Lk)("div",d,[(0,r.bF)(b,{to:"/",class:"menu-item","active-class":"active"},{default:(0,r.k6)(()=>[(0,r.bF)(y,{icon:"home",class:"menu-icon"}),e.isMenuCollapsed?(0,r.Q3)("",!0):((0,r.uX)(),(0,r.CE)("span",f,"主页"))]),_:1}),(0,r.bF)(b,{to:"/log-converter",class:"menu-item","active-class":"active"},{default:(0,r.k6)(()=>[(0,r.bF)(y,{icon:"exchange-alt",class:"menu-icon"}),e.isMenuCollapsed?(0,r.Q3)("",!0):((0,r.uX)(),(0,r.CE)("span",p,"Log 转换工具"))]),_:1})]),(0,r.Lk)("div",g,[(0,r.bF)(b,{to:"/about",class:"menu-item","active-class":"active"},{default:(0,r.k6)(()=>[(0,r.bF)(y,{icon:"info-circle",class:"menu-icon"}),e.isMenuCollapsed?(0,r.Q3)("",!0):((0,r.uX)(),(0,r.CE)("span",m,"关于"))]),_:1}),(0,r.Lk)("div",v,[(0,r.Lk)("div",{class:"menu-toggle",onClick:n[0]||(n[0]=(...n)=>e.toggleMenu&&e.toggleMenu(...n))},[(0,r.bF)(y,{icon:e.isMenuCollapsed?"chevron-right":"chevron-left"},null,8,["icon"])])])])],2),(0,r.Lk)("div",{class:(0,s.C4)(["main-content",{expanded:e.isMenuCollapsed}])},[(0,r.bF)(C)],2)])}var k=o(144),b=o(292),y=(0,r.pM)({name:"App",components:{FontAwesomeIcon:b.gc},setup(){const e=(0,k.KR)(!1),n=()=>{e.value=!e.value};return{isMenuCollapsed:e,toggleMenu:n}}}),C=o(1241);const w=(0,C.A)(y,[["render",h]]);var L=w,_=o(1387);const E={class:"home"},A={class:"tools-grid"},F={class:"tool-icon"},M={class:"tool-features"},j={class:"tool-features"};function x(e,n,o,t,s,i){const c=(0,r.g2)("font-awesome-icon"),l=(0,r.g2)("el-tag"),u=(0,r.g2)("el-card");return(0,r.uX)(),(0,r.CE)("div",E,[n[9]||(n[9]=(0,r.Lk)("div",{class:"page-header"},[(0,r.Lk)("h1",null,"TabKit"),(0,r.Lk)("p",null,"TabKit 中集成了多种实用工具")],-1)),(0,r.Lk)("div",A,[(0,r.bF)(u,{class:"tool-card",shadow:"hover",onClick:n[0]||(n[0]=n=>e.navigateToTool("/log-converter"))},{default:(0,r.k6)(()=>[(0,r.Lk)("div",F,[(0,r.bF)(c,{icon:"exchange-alt"})]),n[3]||(n[3]=(0,r.Lk)("h3",null,"Log 转换工具",-1)),n[4]||(n[4]=(0,r.Lk)("p",null,"支持 ASC、BLF 等日志格式转换，提供文件分割功能。",-1)),(0,r.Lk)("div",M,[(0,r.bF)(l,{size:"small"},{default:(0,r.k6)(()=>n[1]||(n[1]=[(0,r.eW)("格式转换")])),_:1,__:[1]}),(0,r.bF)(l,{size:"small",type:"success"},{default:(0,r.k6)(()=>n[2]||(n[2]=[(0,r.eW)("文件分割")])),_:1,__:[2]})])]),_:1,__:[3,4]}),(0,r.bF)(u,{class:"tool-card coming-soon",shadow:"hover"},{default:(0,r.k6)(()=>[n[6]||(n[6]=(0,r.Lk)("div",{class:"tool-icon"},[(0,r.Lk)("img",{src:a,alt:"Logo",class:"logo-icon"})],-1)),n[7]||(n[7]=(0,r.Lk)("h3",null,"更多工具",-1)),n[8]||(n[8]=(0,r.Lk)("p",null,"更多工具开发中，敬请期待...",-1)),(0,r.Lk)("div",j,[(0,r.bF)(l,{size:"small",type:"info"},{default:(0,r.k6)(()=>n[5]||(n[5]=[(0,r.eW)("即将推出")])),_:1,__:[5]})])]),_:1,__:[6,7,8]})])])}o(4114);var O=(0,r.pM)({name:"HomeView",components:{FontAwesomeIcon:b.gc},setup(){const e=(0,_.rd)(),n=n=>{e.push(n)};return{navigateToTool:n}}});const P=(0,C.A)(O,[["render",x],["__scopeId","data-v-7ecdb9c7"]]);var T=P;const S=[{path:"/",name:"home",component:T},{path:"/log-converter",name:"log-converter",component:()=>o.e(488).then(o.bind(o,4689))},{path:"/about",name:"about",component:()=>o.e(594).then(o.bind(o,7315))}],$=(0,_.aE)({history:(0,_.LA)("/"),routes:S});var I=$,K=o(782),U=(0,K.y$)({state:{},getters:{},mutations:{},actions:{},modules:{}}),B=o(1021),N=o(4373),Q=o(1219),X=o(2933);const z=e=>{if(!e.response||!e.response.data)return e.message||"Unknown error";const n=e.response.data,o=[];n.exceptionMessage&&o.push(n.exceptionMessage);let t=n.innerException;while(t)t.exceptionMessage&&o.push(t.exceptionMessage),t=t.innerException;return 0===o.length?n.message||"An error occurred":o.join("<br>")},q=e=>{if(!e.response||!e.response.data)return void Q.nk.error(e.message||"Unknown error");const n=z(e);X.s.alert(n,"Error",{confirmButtonText:"OK",dangerouslyUseHTMLString:!0,closeOnClickModal:!0,closeOnPressEscape:!0,showClose:!0})},D=e=>{if(e.response&&e.response.data){if("UserCanceled"===e.response.data)return!0;if("UserCanceled"===e.response.data.message)return!0;if("UserCanceled"===e.response.data.errorCode)return!0}return!1},G=()=>{N.A.interceptors.response.use(e=>e,e=>D(e)?(Q.nk.info("Operation cancelled by user"),Promise.reject(e)):(q(e),Promise.reject(e)))};var H=o(7854),W=(o(4188),o(2721)),V=o(7477),R=o(8950),Y=o(2353),Z=o(4996);R.Yv.add(Y.Ubc,Y.Uj9,Y.QLR,Y.h8M,Y.Int,Y.sjs,Y.fny,Y.a$,Y.ao0,Y.$Fj,Y.qFF,Y.Yj9,Y.LqK,Y.tdl,Y.GF6,Y.oZK,Y.gr3,Y.skf,Y.DOu,Y.v02,Y._qq,Y.iW_,Y.Wzs,Y.XkK,Y.pS3,Y.ijD,Y.APi,Y.Vpu,Y.MjD,Y.cbP,Y.yLS,Y.jTw,Y.y_8,Y.Bwz,Z.Vz1);const J=(0,t.Ef)(L);J.component("font-awesome-icon",b.gc);for(const[ee,ne]of Object.entries(V))J.component(ee,ne);G(),J.use(U).use(I).use(H.A,{locale:W.A,size:"default"}).mount("#app"),J.config.errorHandler=(e,n,o)=>{console.error("Vue 全局错误:",e);const t={message:e instanceof Error?e.message:String(e),stack:e instanceof Error?e.stack:"无堆栈信息",vueHookInfo:o,url:window.location.href};B.GQ.logError(t).catch(e=>{console.error("发送错误到服务器失败:",e)})},window.addEventListener("unhandledrejection",e=>{const n={message:e.reason instanceof Error?e.reason.message:"未处理的Promise异常",stack:e.reason instanceof Error?e.reason.stack:"无堆栈信息",url:window.location.href,type:"unhandledrejection"};B.GQ.logError(n).catch(e=>{console.error("发送Promise错误到服务器失败:",e)})}),window.addEventListener("error",e=>{if(e.message){const n={message:e.message,codeInfo:`${e.filename}:${e.lineno}:${e.colno}`,url:window.location.href,type:"global-error"};B.GQ.logError(n).catch(e=>{console.error("发送全局错误到服务器失败:",e)})}})},3935:function(e,n,o){e.exports=o.p+"img/logo.36be161e.svg"}},n={};function o(t){var r=n[t];if(void 0!==r)return r.exports;var s=n[t]={exports:{}};return e[t].call(s.exports,s,s.exports,o),s.exports}o.m=e,function(){var e=[];o.O=function(n,t,r,s){if(!t){var a=1/0;for(u=0;u<e.length;u++){t=e[u][0],r=e[u][1],s=e[u][2];for(var i=!0,c=0;c<t.length;c++)(!1&s||a>=s)&&Object.keys(o.O).every(function(e){return o.O[e](t[c])})?t.splice(c--,1):(i=!1,s<a&&(a=s));if(i){e.splice(u--,1);var l=r();void 0!==l&&(n=l)}}return n}s=s||0;for(var u=e.length;u>0&&e[u-1][2]>s;u--)e[u]=e[u-1];e[u]=[t,r,s]}}(),function(){o.n=function(e){var n=e&&e.__esModule?function(){return e["default"]}:function(){return e};return o.d(n,{a:n}),n}}(),function(){o.d=function(e,n){for(var t in n)o.o(n,t)&&!o.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:n[t]})}}(),function(){o.f={},o.e=function(e){return Promise.all(Object.keys(o.f).reduce(function(n,t){return o.f[t](e,n),n},[]))}}(),function(){o.u=function(e){return"js/"+{488:"log-converter",594:"about"}[e]+"."+{488:"587d7d6d",594:"e1e6dfac"}[e]+".js"}}(),function(){o.miniCssF=function(e){return"css/"+{488:"log-converter",594:"about"}[e]+"."+{488:"7137dcc5",594:"2949009d"}[e]+".css"}}(),function(){o.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){o.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)}}(),function(){var e={},n="tab-kit-web:";o.l=function(t,r,s,a){if(e[t])e[t].push(r);else{var i,c;if(void 0!==s)for(var l=document.getElementsByTagName("script"),u=0;u<l.length;u++){var d=l[u];if(d.getAttribute("src")==t||d.getAttribute("data-webpack")==n+s){i=d;break}}i||(c=!0,i=document.createElement("script"),i.charset="utf-8",i.timeout=120,o.nc&&i.setAttribute("nonce",o.nc),i.setAttribute("data-webpack",n+s),i.src=t),e[t]=[r];var f=function(n,o){i.onerror=i.onload=null,clearTimeout(p);var r=e[t];if(delete e[t],i.parentNode&&i.parentNode.removeChild(i),r&&r.forEach(function(e){return e(o)}),n)return n(o)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=f.bind(null,i.onerror),i.onload=f.bind(null,i.onload),c&&document.head.appendChild(i)}}}(),function(){o.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){o.p="/"}(),function(){if("undefined"!==typeof document){var e=function(e,n,t,r,s){var a=document.createElement("link");a.rel="stylesheet",a.type="text/css",o.nc&&(a.nonce=o.nc);var i=function(o){if(a.onerror=a.onload=null,"load"===o.type)r();else{var t=o&&o.type,i=o&&o.target&&o.target.href||n,c=new Error("Loading CSS chunk "+e+" failed.\n("+t+": "+i+")");c.name="ChunkLoadError",c.code="CSS_CHUNK_LOAD_FAILED",c.type=t,c.request=i,a.parentNode&&a.parentNode.removeChild(a),s(c)}};return a.onerror=a.onload=i,a.href=n,t?t.parentNode.insertBefore(a,t.nextSibling):document.head.appendChild(a),a},n=function(e,n){for(var o=document.getElementsByTagName("link"),t=0;t<o.length;t++){var r=o[t],s=r.getAttribute("data-href")||r.getAttribute("href");if("stylesheet"===r.rel&&(s===e||s===n))return r}var a=document.getElementsByTagName("style");for(t=0;t<a.length;t++){r=a[t],s=r.getAttribute("data-href");if(s===e||s===n)return r}},t=function(t){return new Promise(function(r,s){var a=o.miniCssF(t),i=o.p+a;if(n(a,i))return r();e(t,i,null,r,s)})},r={524:0};o.f.miniCss=function(e,n){var o={488:1,594:1};r[e]?n.push(r[e]):0!==r[e]&&o[e]&&n.push(r[e]=t(e).then(function(){r[e]=0},function(n){throw delete r[e],n}))}}}(),function(){var e={524:0};o.f.j=function(n,t){var r=o.o(e,n)?e[n]:void 0;if(0!==r)if(r)t.push(r[2]);else{var s=new Promise(function(o,t){r=e[n]=[o,t]});t.push(r[2]=s);var a=o.p+o.u(n),i=new Error,c=function(t){if(o.o(e,n)&&(r=e[n],0!==r&&(e[n]=void 0),r)){var s=t&&("load"===t.type?"missing":t.type),a=t&&t.target&&t.target.src;i.message="Loading chunk "+n+" failed.\n("+s+": "+a+")",i.name="ChunkLoadError",i.type=s,i.request=a,r[1](i)}};o.l(a,c,"chunk-"+n,n)}},o.O.j=function(n){return 0===e[n]};var n=function(n,t){var r,s,a=t[0],i=t[1],c=t[2],l=0;if(a.some(function(n){return 0!==e[n]})){for(r in i)o.o(i,r)&&(o.m[r]=i[r]);if(c)var u=c(o)}for(n&&n(t);l<a.length;l++)s=a[l],o.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return o.O(u)},t=self["webpackChunktab_kit_web"]=self["webpackChunktab_kit_web"]||[];t.forEach(n.bind(null,0)),t.push=n.bind(null,t.push.bind(t))}();var t=o.O(void 0,[504],function(){return o(1803)});t=o.O(t)})();
//# sourceMappingURL=app.c90c031e.js.map