[{"D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\main.ts": "1", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\App.vue": "2", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\api\\appApi.ts": "3", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\router\\index.ts": "4", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\HomeView.vue": "5", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\AboutView.vue": "6", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\LogConverterView.vue": "7", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\utils\\errorHandler.ts": "8", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\store\\index.ts": "9"}, {"size": 4003, "mtime": 1750409787546, "results": "10", "hashOfConfig": "11"}, {"size": 5435, "mtime": 1750398308051, "results": "12", "hashOfConfig": "11"}, {"size": 3252, "mtime": 1750410148666, "results": "13", "hashOfConfig": "11"}, {"size": 667, "mtime": 1750325656308, "results": "14", "hashOfConfig": "11"}, {"size": 3211, "mtime": 1750401868461, "results": "15", "hashOfConfig": "11"}, {"size": 6223, "mtime": 1750410628747, "results": "16", "hashOfConfig": "11"}, {"size": 11416, "mtime": 1750410164246, "results": "17", "hashOfConfig": "11"}, {"size": 2910, "mtime": 1750325656311, "results": "18", "hashOfConfig": "11"}, {"size": 145, "mtime": 1750325656297, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "22"}, "12tt6fj", {"filePath": "23", "messages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "33", "messages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "35"}, {"filePath": "36", "messages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "38"}, {"filePath": "39", "messages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\main.ts", ["41", "42", "43", "44"], "import { createApp } from \"vue\";\nimport App from \"./App.vue\";\nimport router from \"./router\";\nimport store from \"./store\";\n\nimport { appApi, type ErrorData } from './api/appApi' // 导入 appApi 而不是 axios\nimport { setupErrorHandler } from './utils/errorHandler' // 导入错误处理器\n\n// 引入 Element Plus\nimport ElementPlus from 'element-plus'\nimport 'element-plus/dist/index.css'\nimport zhCn from 'element-plus/dist/locale/zh-cn.mjs'\n\n// 设置 Element Plus 主题变量\nimport * as ElementPlusIconsVue from '@element-plus/icons-vue'\nimport './styles/element-variables.css' // 需要创建这个文件来自定义主题\n\n// 引入 FontAwesome\nimport { library } from '@fortawesome/fontawesome-svg-core'\nimport { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'\nimport {\n  faCogs, faFolderOpen, faPlus, faFileAlt,\n  faHistory, faTrashCan, faFileExcel, faClock,\n  faFolder, faChartBar, faProjectDiagram,\n  faClockRotateLeft, faFileCircleExclamation,\n  faAngleDown, faAngleUp, faExpand, faCompress,\n  faUpRightAndDownLeftFromCenter, faDownLeftAndUpRightToCenter,\n  faHome, faExchangeAlt, faInfoCircle, faChevronLeft, faChevronRight,\n  faEye, faPlay, faStop, faRefresh, faSearch, faDownload, faTrash,\n  faCode, faEnvelope, faGlobe, faCommentAlt\n} from '@fortawesome/free-solid-svg-icons'\n\nimport { faGithub, faTeamspeak } from '@fortawesome/free-brands-svg-icons'\n\n// 添加需要使用的图标到库中\nlibrary.add(\n  faCogs, faFolderOpen, faPlus, faFileAlt,\n  faHistory, faTrashCan, faFileExcel, faClock,\n  faFolder, faChartBar, faProjectDiagram,\n  faClockRotateLeft, faFileCircleExclamation,\n  faAngleDown, faAngleUp, faExpand, faCompress,\n  faUpRightAndDownLeftFromCenter, faDownLeftAndUpRightToCenter,\n  faHome, faExchangeAlt, faInfoCircle, faChevronLeft, faChevronRight,\n  faEye, faPlay, faStop, faRefresh, faSearch, faDownload, faTrash,\n  faCode, faEnvelope, faGlobe, faGithub, faTeamspeak, faCommentAlt\n)\n\nconst app = createApp(App)\n\n// 全局注册 FontAwesome 组件\napp.component('font-awesome-icon', FontAwesomeIcon)\n\n// 全局注册所有图标\nfor (const [key, component] of Object.entries(ElementPlusIconsVue)) {\n  app.component(key, component)\n}\n\n// 设置全局错误处理\nsetupErrorHandler()\n\napp.use(store)\n   .use(router)\n   .use(ElementPlus, {\n     locale: zhCn,\n     size: 'default'\n   })\n   .mount('#app')\n\n// 定义 sendError 类型\ntype SendErrorType = Error | unknown;\n\n// 全局异常处理\napp.config.errorHandler = (err: unknown, vm, info) => {\n  // 控制台输出错误\n  console.error(\"Vue 全局错误:\", err);\n\n  // 将错误发送到后端\n  const errorData: ErrorData = {\n    message: err instanceof Error ? err.message : String(err),\n    stack: err instanceof Error ? err.stack : \"无堆栈信息\",\n    vueHookInfo: info, // 更新字段名\n    url: window.location.href,\n  };\n\n  appApi.logError(errorData).catch((sendError: SendErrorType) => {\n    console.error(\"发送错误到服务器失败:\", sendError);\n  });\n};\n\n// 捕获未处理的Promise异常\nwindow.addEventListener(\"unhandledrejection\", (event) => {\n  const errorData: ErrorData = {\n    message:\n      event.reason instanceof Error\n        ? event.reason.message\n        : \"未处理的Promise异常\",\n    stack: event.reason instanceof Error ? event.reason.stack : \"无堆栈信息\",\n    url: window.location.href,\n    type: \"unhandledrejection\",\n  };\n\n  appApi.logError(errorData).catch((sendError: SendErrorType) => {\n    console.error(\"发送Promise错误到服务器失败:\", sendError);\n  });\n});\n\n// 捕获全局JS错误\nwindow.addEventListener(\"error\", (event) => {\n  // 过滤资源加载错误\n  if (event.message) {\n    const errorData: ErrorData = {\n      message: event.message,\n      codeInfo: `${event.filename}:${event.lineno}:${event.colno}`,\n      url: window.location.href,\n      type: \"global-error\",\n    };\n\n    appApi.logError(errorData).catch((sendError: SendErrorType) => {\n      console.error(\"发送全局错误到服务器失败:\", sendError);\n    });\n  }\n});\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\App.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\api\\appApi.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\router\\index.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\HomeView.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\AboutView.vue", ["45"], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\LogConverterView.vue", ["46", "47", "48", "49"], "<template>\n  <div class=\"log-converter\">\n    <!-- 标题栏 -->\n    <div class=\"title-bar\">Log 转换工具</div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"converter-content\">\n      <div class=\"config-row\">\n        <div class=\"config-item\" style=\"width:80%;\">\n          <label class=\"config-label\" style=\"margin-bottom: 10px;\">Log 文件路径:</label>\n          <el-input v-model=\"fileForm.filePath\" placeholder=\"请选择一个 Log 文件或输入路径，支持格式：.asc .blf\" @blur=\"analyzeFile\">\n            <template #append>\n              <el-button @click=\"selectFile\">\n                选择 Log 文件\n              </el-button>\n            </template>\n          </el-input>\n        </div>\n      </div>\n\n      <div class=\"config-row\">\n        <div class=\"config-item\">\n          <label class=\"config-label\">目标格式</label>\n          <el-radio-group v-model=\"processRequest.targetFormat\">\n            <el-radio :value=\"1\">ASC</el-radio>\n            <el-radio :value=\"2\">BLF</el-radio>\n          </el-radio-group>\n        </div>\n\n        <div class=\"config-item\">\n          <label class=\"config-label\">启用分割</label>\n          <el-switch v-model=\"processRequest.enableSplit\" @change=\"onSplitToggle\" />\n        </div>\n\n        <div class=\"config-item\" v-if=\"processRequest.enableSplit\">\n          <label class=\"config-label\">分割文件数</label>\n          <el-input-number v-model=\"splitFileCount\" :min=\"1\" :max=\"100\" @change=\"onSplitCountChange\"\n            class=\"split-count-input\" />\n        </div>\n      </div>\n\n      <!-- 操作控制区域 -->\n      <div class=\"action-buttons\">\n        <el-button type=\"primary\" :disabled=\"!fileForm.filePath\" @click=\"startProcess\" :loading=\"isProcessing\">\n          开始转换\n        </el-button>\n\n        <el-button type=\"danger\" :disabled=\"!isProcessing\" @click=\"cancelProcess\">\n          取消转换\n        </el-button>\n      </div>\n\n      <!-- 进度显示 -->\n      <div v-if=\"progress\" class=\"progress-section\">\n        <h4>转换进度</h4>\n        <el-progress :percentage=\"progress.overallProgressPercentage\"\n          :status=\"progress.isCompleted ? 'success' : 'active'\" />\n        <p class=\"current-operation\">{{ progress.currentOperation }}</p>\n\n        <!-- 文件处理进度 -->\n        <div v-if=\"progress.fileProgresses && progress.fileProgresses.length > 0\" class=\"file-progress\">\n          <div class=\"file-progress-list\">\n            <div v-for=\"(fileProgress, index) in progress.fileProgresses\" :key=\"index\" class=\"file-progress-item\">\n              <div class=\"file-name\">{{ fileProgress.fileName }}</div>\n              <el-progress :percentage=\"fileProgress.progressPercentage\" :show-text=\"false\" size=\"small\"\n                style=\"width: 100px;\" />\n              <div class=\"file-status\">{{ getStatusName(fileProgress.status) }}</div>\n              <div class=\"file-actions\">\n                <el-button v-if=\"fileProgress.status === 'Completed'\" type=\"text\" size=\"small\"\n                  @click=\"openFileInExplorer(fileProgress.filePath)\" class=\"explorer-button\" title=\"在文件夹中显示\">\n                  <font-awesome-icon icon=\"folder-open\" />\n                </el-button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 状态栏 -->\n    <div class=\"status-bar\">\n      <span v-if=\"fileForm.filePath\">\n        文件: 已选择 - {{ fileForm.filePath }}\n      </span>\n      <span v-if=\"!fileForm.filePath\">请选择 Log 文件</span>\n      <span v-if=\"isProcessing\" class=\"processing-status\">\n        转换中... {{ progress?.overallProgressPercentage || 0 }}%\n      </span>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, ref, onUnmounted } from \"vue\";\nimport {\n  appApi,\n  DataLogProcessRequest,\n  ProcessProgress,\n  DataLogFormat,\n  ProcessStatus\n} from \"@/api/appApi\";\n\nexport default defineComponent({\n  name: \"LogConverterView\",\n  setup() {\n    // 文件相关\n    const fileForm = ref({ filePath: '' });\n\n    // 处理请求\n    const processRequest = ref<DataLogProcessRequest>({\n      sourceFilePath: '',\n      targetFormat: DataLogFormat.Blf,\n      enableSplit: false,\n      splitFileCount: 1\n    });\n\n    const splitFileCount = ref(1);\n    const currentTaskId = ref<string | null>(null);\n    const progress = ref<ProcessProgress | null>(null);\n\n    // 状态控制\n    const isProcessing = ref(false);\n\n    let progressTimer: number | null = null;\n\n    // 文件选择\n    const selectFile = async () => {\n      const response = await appApi.dataLogConvert.selectFile();\n      const result = response.data;\n\n      if (result) {\n        fileForm.value.filePath = result;\n        processRequest.value.sourceFilePath = result;\n      }\n    };\n\n    const analyzeFile = async () => {\n      if (!fileForm.value.filePath) return;\n      processRequest.value.sourceFilePath = fileForm.value.filePath;\n    };\n\n    // 分割开关变化\n    const onSplitToggle = () => {\n      if (processRequest.value.enableSplit) {\n        splitFileCount.value = 1;\n        processRequest.value.splitFileCount = 1;\n      }\n    };\n\n    // 分割文件数变化\n    const onSplitCountChange = () => {\n      processRequest.value.splitFileCount = splitFileCount.value;\n    };\n\n    const startProcess = async () => {\n      isProcessing.value = true;\n      try {\n        const response = await appApi.dataLogConvert.startProcess(processRequest.value);\n        currentTaskId.value = response.data;\n\n        // 开始轮询进度\n        startProgressPolling();\n      } catch (error) {\n        console.error('开始处理失败:', error);\n        isProcessing.value = false;\n      }\n    };\n\n    const startProgressPolling = () => {\n      if (!currentTaskId.value) return;\n\n      progressTimer = window.setInterval(async () => {\n        try {\n          const response = await appApi.dataLogConvert.getProgress(currentTaskId.value!);\n          progress.value = response.data;\n\n          if (progress.value?.isCompleted) {\n            stopProgressPolling();\n            isProcessing.value = false;\n          }\n        } catch (error) {\n          console.error('获取进度失败:', error);\n        }\n      }, 1000);\n    };\n\n    const stopProgressPolling = () => {\n      if (progressTimer) {\n        clearInterval(progressTimer);\n        progressTimer = null;\n      }\n    };\n\n    const cancelProcess = async () => {\n      if (!currentTaskId.value) return;\n\n      try {\n        await appApi.dataLogConvert.cancelProcess(currentTaskId.value);\n        stopProgressPolling();\n        isProcessing.value = false;\n      } catch (error) {\n        console.error('取消处理失败:', error);\n      }\n    };\n\n    const getStatusName = (status: ProcessStatus): string => {\n      switch (status) {\n        case ProcessStatus.Pending: return '等待中';\n        case ProcessStatus.Processing: return '处理中';\n        case ProcessStatus.Completed: return '已完成';\n        case ProcessStatus.Failed: return '失败';\n        case ProcessStatus.Cancelled: return '已取消';\n        default: return '未知';\n      }\n    };\n\n    // 在文件资源管理器中显示文件\n    const openFileInExplorer = async (filePath: string) => {\n      await appApi.explorer.openExplorer(filePath);\n    };\n\n    onUnmounted(() => {\n      stopProgressPolling();\n    });\n\n    return {\n      fileForm,\n      processRequest,\n      splitFileCount,\n      progress,\n      isProcessing,\n      selectFile,\n      analyzeFile,\n      onSplitToggle,\n      onSplitCountChange,\n      startProcess,\n      cancelProcess,\n      getStatusName,\n      openFileInExplorer\n    };\n  },\n});\n</script>\n\n<style scoped>\n.log-converter {\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  overflow: hidden;\n}\n\n.title-bar {\n  font-size: 18px;\n  margin: 0 20px;\n  padding: 10px;\n  color: var(--el-text-color-primary);\n  font-weight: bold;\n  border-bottom: solid var(--el-border-color-base) 1px;\n  flex-shrink: 0;\n  height: 60px;\n  display: flex;\n  align-items: center;\n}\n\n.converter-content {\n  flex: 1;\n  padding: 20px;\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  margin: 0 10px;\n}\n\n.file-info {\n  background-color: #f8f9fa;\n  margin: 0;\n  border-radius: 0;\n}\n\n.file-info h4 {\n  color: #2c3e50;\n  margin-bottom: 15px;\n  font-size: 1rem;\n}\n\n.info-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 10px;\n  margin-bottom: 15px;\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n}\n\n.info-item .label {\n  color: #7f8c8d;\n  font-weight: 500;\n}\n\n.info-item .value {\n  color: #2c3e50;\n  font-weight: bold;\n}\n\n.file-actions {\n  display: flex;\n  gap: 10px;\n  min-width: 30px;\n}\n\n.error-info {\n  margin-top: 15px;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 10px;\n}\n\n.progress-section {\n  border-top: 1px solid #e9ecef;\n  padding-top: 10px;\n}\n\n.progress-section h4 {\n  color: #2c3e50;\n  margin-bottom: 15px;\n  font-size: 1rem;\n}\n\n.current-operation {\n  color: #7f8c8d;\n  margin-top: 10px;\n  font-size: 0.9rem;\n}\n\n.file-progress {\n  margin-top: 20px;\n}\n\n.file-progress h5 {\n  color: #2c3e50;\n  margin-bottom: 10px;\n  font-size: 0.9rem;\n}\n\n.file-progress-list {\n  overflow-y: auto;\n}\n\n.file-progress-item {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  padding: 8px;\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n  margin-bottom: 6px;\n  background-color: #fff;\n  max-width: 78vw;\n}\n\n.file-progress-item:last-child {\n  margin-bottom: 0;\n}\n\n.file-progress-item .file-name {\n  flex: 1;\n  font-size: 0.9rem;\n  color: #2c3e50;\n  font-weight: 500;\n}\n\n.file-progress-item .file-status {\n  width: 80px;\n  font-size: 0.8rem;\n  color: #7f8c8d;\n}\n\n.file-progress-item .file-actions {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n}\n\n.explorer-button {\n  padding: 4px 6px !important;\n  min-height: 24px !important;\n  color: #666 !important;\n  transition: color 0.3s ease;\n}\n\n.explorer-button:hover {\n  color: var(--el-color-primary) !important;\n}\n\n.completion-section {\n  margin-top: 20px;\n  padding-top: 20px;\n  border-top: 1px solid var(--el-border-color-light);\n}\n\n.status-bar {\n  margin: 0 10px;\n  height: 54px;\n  background-color: var(--el-fill-color-light);\n  padding: 10px 20px;\n  border-top: 1px solid var(--el-border-color-base);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 0.9rem;\n  color: var(--el-text-color-regular);\n  flex-shrink: 0;\n\n  span {\n    white-space: nowrap;\n    text-overflow: ellipsis;\n    overflow: hidden;\n    max-width: 60vw;\n  }\n}\n\n.processing-status {\n  color: var(--el-color-primary);\n  font-weight: bold;\n}\n\n.config-row {\n  display: flex;\n  align-items: flex-start;\n  gap: 60px;\n  flex-wrap: wrap;\n}\n\n.config-item {\n  display: flex;\n  flex-direction: column;\n}\n\n.config-label {\n  font-size: 14px;\n  color: var(--el-text-color-regular);\n  font-weight: 500;\n  margin-bottom: 4px;\n}\n\n.split-count-input {\n  width: 120px;\n}\n\n@media (max-width: 768px) {\n  .converter-content {\n    padding: 10px;\n  }\n\n  .config-row {\n    flex-direction: column;\n    gap: 15px;\n  }\n\n  .config-item {\n    min-width: auto;\n    width: 100%;\n  }\n\n  .action-buttons {\n    flex-direction: column;\n  }\n\n  .file-progress-item {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 5px;\n  }\n\n  .file-progress-item .file-actions {\n    align-self: flex-end;\n  }\n\n  .status-bar {\n    flex-direction: column;\n    gap: 5px;\n    text-align: center;\n  }\n}\n</style>\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\utils\\errorHandler.ts", ["50", "51", "52"], "import axios from 'axios';\nimport { ElMessage, ElMessageBox } from 'element-plus';\n\n// 格式化错误信息，显示所有层级的异常\nconst formatErrorMessage = (error: any): string => {\n  if (!error.response || !error.response.data) {\n    return error.message || 'Unknown error';\n  }\n\n  const errorData = error.response.data;\n  const errorMessages = [];\n\n  // 添加主异常信息\n  if (errorData.exceptionMessage) {\n    errorMessages.push(errorData.exceptionMessage);\n  }\n\n  // 递归添加所有内部异常信息\n  let currentException = errorData.innerException;\n  while (currentException) {\n    if (currentException.exceptionMessage) {\n      errorMessages.push(currentException.exceptionMessage);\n    }\n    currentException = currentException.innerException;\n  }\n\n  // 如果没有找到任何异常信息，返回通用错误消息\n  if (errorMessages.length === 0) {\n    return errorData.message || 'An error occurred';\n  }\n\n  // 返回所有异常信息，每个一行\n  return errorMessages.join('<br>');\n};\n\n// 显示详细错误信息\nconst showDetailedError = (error: any): void => {\n  if (!error.response || !error.response.data) {\n    ElMessage.error(error.message || 'Unknown error');\n    return;\n  }\n\n  // 获取格式化的错误信息\n  const errorMessage = formatErrorMessage(error);\n\n  // 使用对话框显示详细错误信息\n  ElMessageBox.alert(\n    errorMessage,\n    'Error',\n    {\n      confirmButtonText: 'OK',\n      dangerouslyUseHTMLString: true,\n      closeOnClickModal: true,  // 允许点击空白区域关闭\n      closeOnPressEscape: true, // 允许按ESC键关闭\n      showClose: true           // 显示右上角关闭按钮\n    }\n  );\n};\n\n// 检查是否为用户取消操作\nconst isUserCanceled = (error: any): boolean => {\n  // 检查错误响应数据\n  if (error.response && error.response.data) {\n    // 检查直接等于字符串的情况\n    if (error.response.data === 'UserCanceled') {\n      return true;\n    }\n\n    // 检查错误消息字段\n    if (error.response.data.message === 'UserCanceled') {\n      return true;\n    }\n\n    // 检查错误代码字段\n    if (error.response.data.errorCode === 'UserCanceled') {\n      return true;\n    }\n  }\n\n  return false;\n};\n\n// 设置响应拦截器\nexport const setupErrorHandler = (): void => {\n  axios.interceptors.response.use(\n    response => response,\n    error => {\n      // 检查是否为用户取消操作\n      if (isUserCanceled(error)) {\n        // 用户取消操作，显示信息提示而不是错误\n        ElMessage.info(\"Operation cancelled by user\");\n\n        // 继续抛出错误，以便调用者可以进行额外处理\n        return Promise.reject(error);\n      }\n\n      // 处理其他错误\n      showDetailedError(error);\n\n      // 继续抛出错误，以便调用者可以进行额外处理\n      return Promise.reject(error);\n    }\n  );\n};\n\nexport default setupErrorHandler;\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\store\\index.ts", [], {"ruleId": "53", "severity": 1, "message": "54", "line": 75, "column": 3, "nodeType": "55", "messageId": "56", "endLine": 75, "endColumn": 16}, {"ruleId": "53", "severity": 1, "message": "54", "line": 86, "column": 5, "nodeType": "55", "messageId": "56", "endLine": 86, "endColumn": 18}, {"ruleId": "53", "severity": 1, "message": "54", "line": 103, "column": 5, "nodeType": "55", "messageId": "56", "endLine": 103, "endColumn": 18}, {"ruleId": "53", "severity": 1, "message": "54", "line": 119, "column": 7, "nodeType": "55", "messageId": "56", "endLine": 119, "endColumn": 20}, {"ruleId": "57", "severity": 1, "message": "58", "line": 100, "column": 11, "nodeType": "59", "messageId": "60", "endLine": 100, "endColumn": 20}, {"ruleId": "53", "severity": 1, "message": "54", "line": 164, "column": 9, "nodeType": "55", "messageId": "56", "endLine": 164, "endColumn": 22}, {"ruleId": "61", "severity": 1, "message": "62", "line": 174, "column": 68, "nodeType": "63", "messageId": "64", "endLine": 174, "endColumn": 88}, {"ruleId": "53", "severity": 1, "message": "54", "line": 182, "column": 11, "nodeType": "55", "messageId": "56", "endLine": 182, "endColumn": 24}, {"ruleId": "53", "severity": 1, "message": "54", "line": 202, "column": 9, "nodeType": "55", "messageId": "56", "endLine": 202, "endColumn": 22}, {"ruleId": "65", "severity": 1, "message": "66", "line": 5, "column": 36, "nodeType": "67", "messageId": "68", "endLine": 5, "endColumn": 39, "suggestions": "69"}, {"ruleId": "65", "severity": 1, "message": "66", "line": 37, "column": 35, "nodeType": "67", "messageId": "68", "endLine": 37, "endColumn": 38, "suggestions": "70"}, {"ruleId": "65", "severity": 1, "message": "66", "line": 61, "column": 32, "nodeType": "67", "messageId": "68", "endLine": 61, "endColumn": 35, "suggestions": "71"}, "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", "@typescript-eslint/no-unused-vars", "'openTeams' is assigned a value but never used.", "Identifier", "unusedVar", "@typescript-eslint/no-non-null-assertion", "Forbidden non-null assertion.", "TSNonNullExpression", "noNonNull", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["72", "73"], ["74", "75"], ["76", "77"], {"messageId": "78", "fix": "79", "desc": "80"}, {"messageId": "81", "fix": "82", "desc": "83"}, {"messageId": "78", "fix": "84", "desc": "80"}, {"messageId": "81", "fix": "85", "desc": "83"}, {"messageId": "78", "fix": "86", "desc": "80"}, {"messageId": "81", "fix": "87", "desc": "83"}, "suggestUnknown", {"range": "88", "text": "89"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "88", "text": "90"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "91", "text": "89"}, {"range": "91", "text": "90"}, {"range": "92", "text": "89"}, {"range": "92", "text": "90"}, [140, 143], "unknown", "never", [931, 934], [1479, 1482]]