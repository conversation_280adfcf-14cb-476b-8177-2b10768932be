[{"D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\main.ts": "1", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\App.vue": "2", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\api\\appApi.ts": "3", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\router\\index.ts": "4", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\HomeView.vue": "5", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\AboutView.vue": "6", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\LogConverterView.vue": "7", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\utils\\errorHandler.ts": "8", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\store\\index.ts": "9"}, {"size": 3949, "mtime": 1750325656295, "results": "10", "hashOfConfig": "11"}, {"size": 5435, "mtime": 1750398308051, "results": "12", "hashOfConfig": "11"}, {"size": 2617, "mtime": 1750398658283, "results": "13", "hashOfConfig": "11"}, {"size": 667, "mtime": 1750325656308, "results": "14", "hashOfConfig": "11"}, {"size": 3211, "mtime": 1750398344672, "results": "15", "hashOfConfig": "11"}, {"size": 5318, "mtime": 1750398137012, "results": "16", "hashOfConfig": "11"}, {"size": 10327, "mtime": 1750400756099, "results": "17", "hashOfConfig": "11"}, {"size": 2910, "mtime": 1750325656311, "results": "18", "hashOfConfig": "11"}, {"size": 145, "mtime": 1750325656297, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "22"}, "12tt6fj", {"filePath": "23", "messages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "35", "messages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "37"}, {"filePath": "38", "messages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\main.ts", ["40", "41", "42", "43"], "import { createApp } from \"vue\";\nimport App from \"./App.vue\";\nimport router from \"./router\";\nimport store from \"./store\";\n\nimport { appApi, type ErrorData } from './api/appApi' // 导入 appApi 而不是 axios\nimport { setupErrorHandler } from './utils/errorHandler' // 导入错误处理器\n\n// 引入 Element Plus\nimport ElementPlus from 'element-plus'\nimport 'element-plus/dist/index.css'\nimport zhCn from 'element-plus/dist/locale/zh-cn.mjs'\n\n// 设置 Element Plus 主题变量\nimport * as ElementPlusIconsVue from '@element-plus/icons-vue'\nimport './styles/element-variables.css' // 需要创建这个文件来自定义主题\n\n// 引入 FontAwesome\nimport { library } from '@fortawesome/fontawesome-svg-core'\nimport { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'\nimport {\n  faCogs, faFolderOpen, faPlus, faFileAlt,\n  faHistory, faTrashCan, faFileExcel, faClock,\n  faFolder, faChartBar, faProjectDiagram,\n  faClockRotateLeft, faFileCircleExclamation,\n  faAngleDown, faAngleUp, faExpand, faCompress,\n  faUpRightAndDownLeftFromCenter, faDownLeftAndUpRightToCenter,\n  faHome, faExchangeAlt, faInfoCircle, faChevronLeft, faChevronRight,\n  faEye, faPlay, faStop, faRefresh, faSearch, faDownload, faTrash,\n  faCode, faEnvelope, faGlobe\n} from '@fortawesome/free-solid-svg-icons'\n\nimport { faGithub } from '@fortawesome/free-brands-svg-icons'\n\n// 添加需要使用的图标到库中\nlibrary.add(\n  faCogs, faFolderOpen, faPlus, faFileAlt,\n  faHistory, faTrashCan, faFileExcel, faClock,\n  faFolder, faChartBar, faProjectDiagram,\n  faClockRotateLeft, faFileCircleExclamation,\n  faAngleDown, faAngleUp, faExpand, faCompress,\n  faUpRightAndDownLeftFromCenter, faDownLeftAndUpRightToCenter,\n  faHome, faExchangeAlt, faInfoCircle, faChevronLeft, faChevronRight,\n  faEye, faPlay, faStop, faRefresh, faSearch, faDownload, faTrash,\n  faCode, faEnvelope, faGlobe, faGithub\n)\n\nconst app = createApp(App)\n\n// 全局注册 FontAwesome 组件\napp.component('font-awesome-icon', FontAwesomeIcon)\n\n// 全局注册所有图标\nfor (const [key, component] of Object.entries(ElementPlusIconsVue)) {\n  app.component(key, component)\n}\n\n// 设置全局错误处理\nsetupErrorHandler()\n\napp.use(store)\n   .use(router)\n   .use(ElementPlus, {\n     locale: zhCn,\n     size: 'default'\n   })\n   .mount('#app')\n\n// 定义 sendError 类型\ntype SendErrorType = Error | unknown;\n\n// 全局异常处理\napp.config.errorHandler = (err: unknown, vm, info) => {\n  // 控制台输出错误\n  console.error(\"Vue 全局错误:\", err);\n\n  // 将错误发送到后端\n  const errorData: ErrorData = {\n    message: err instanceof Error ? err.message : String(err),\n    stack: err instanceof Error ? err.stack : \"无堆栈信息\",\n    vueHookInfo: info, // 更新字段名\n    url: window.location.href,\n  };\n\n  appApi.logError(errorData).catch((sendError: SendErrorType) => {\n    console.error(\"发送错误到服务器失败:\", sendError);\n  });\n};\n\n// 捕获未处理的Promise异常\nwindow.addEventListener(\"unhandledrejection\", (event) => {\n  const errorData: ErrorData = {\n    message:\n      event.reason instanceof Error\n        ? event.reason.message\n        : \"未处理的Promise异常\",\n    stack: event.reason instanceof Error ? event.reason.stack : \"无堆栈信息\",\n    url: window.location.href,\n    type: \"unhandledrejection\",\n  };\n\n  appApi.logError(errorData).catch((sendError: SendErrorType) => {\n    console.error(\"发送Promise错误到服务器失败:\", sendError);\n  });\n});\n\n// 捕获全局JS错误\nwindow.addEventListener(\"error\", (event) => {\n  // 过滤资源加载错误\n  if (event.message) {\n    const errorData: ErrorData = {\n      message: event.message,\n      codeInfo: `${event.filename}:${event.lineno}:${event.colno}`,\n      url: window.location.href,\n      type: \"global-error\",\n    };\n\n    appApi.logError(errorData).catch((sendError: SendErrorType) => {\n      console.error(\"发送全局错误到服务器失败:\", sendError);\n    });\n  }\n});\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\App.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\api\\appApi.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\router\\index.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\HomeView.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\AboutView.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\LogConverterView.vue", ["44", "45", "46", "47"], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\utils\\errorHandler.ts", ["48", "49", "50"], "import axios from 'axios';\nimport { ElMessage, ElMessageBox } from 'element-plus';\n\n// 格式化错误信息，显示所有层级的异常\nconst formatErrorMessage = (error: any): string => {\n  if (!error.response || !error.response.data) {\n    return error.message || 'Unknown error';\n  }\n\n  const errorData = error.response.data;\n  const errorMessages = [];\n\n  // 添加主异常信息\n  if (errorData.exceptionMessage) {\n    errorMessages.push(errorData.exceptionMessage);\n  }\n\n  // 递归添加所有内部异常信息\n  let currentException = errorData.innerException;\n  while (currentException) {\n    if (currentException.exceptionMessage) {\n      errorMessages.push(currentException.exceptionMessage);\n    }\n    currentException = currentException.innerException;\n  }\n\n  // 如果没有找到任何异常信息，返回通用错误消息\n  if (errorMessages.length === 0) {\n    return errorData.message || 'An error occurred';\n  }\n\n  // 返回所有异常信息，每个一行\n  return errorMessages.join('<br>');\n};\n\n// 显示详细错误信息\nconst showDetailedError = (error: any): void => {\n  if (!error.response || !error.response.data) {\n    ElMessage.error(error.message || 'Unknown error');\n    return;\n  }\n\n  // 获取格式化的错误信息\n  const errorMessage = formatErrorMessage(error);\n\n  // 使用对话框显示详细错误信息\n  ElMessageBox.alert(\n    errorMessage,\n    'Error',\n    {\n      confirmButtonText: 'OK',\n      dangerouslyUseHTMLString: true,\n      closeOnClickModal: true,  // 允许点击空白区域关闭\n      closeOnPressEscape: true, // 允许按ESC键关闭\n      showClose: true           // 显示右上角关闭按钮\n    }\n  );\n};\n\n// 检查是否为用户取消操作\nconst isUserCanceled = (error: any): boolean => {\n  // 检查错误响应数据\n  if (error.response && error.response.data) {\n    // 检查直接等于字符串的情况\n    if (error.response.data === 'UserCanceled') {\n      return true;\n    }\n\n    // 检查错误消息字段\n    if (error.response.data.message === 'UserCanceled') {\n      return true;\n    }\n\n    // 检查错误代码字段\n    if (error.response.data.errorCode === 'UserCanceled') {\n      return true;\n    }\n  }\n\n  return false;\n};\n\n// 设置响应拦截器\nexport const setupErrorHandler = (): void => {\n  axios.interceptors.response.use(\n    response => response,\n    error => {\n      // 检查是否为用户取消操作\n      if (isUserCanceled(error)) {\n        // 用户取消操作，显示信息提示而不是错误\n        ElMessage.info(\"Operation cancelled by user\");\n\n        // 继续抛出错误，以便调用者可以进行额外处理\n        return Promise.reject(error);\n      }\n\n      // 处理其他错误\n      showDetailedError(error);\n\n      // 继续抛出错误，以便调用者可以进行额外处理\n      return Promise.reject(error);\n    }\n  );\n};\n\nexport default setupErrorHandler;\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\store\\index.ts", [], {"ruleId": "51", "severity": 1, "message": "52", "line": 75, "column": 3, "nodeType": "53", "messageId": "54", "endLine": 75, "endColumn": 16}, {"ruleId": "51", "severity": 1, "message": "52", "line": 86, "column": 5, "nodeType": "53", "messageId": "54", "endLine": 86, "endColumn": 18}, {"ruleId": "51", "severity": 1, "message": "52", "line": 103, "column": 5, "nodeType": "53", "messageId": "54", "endLine": 103, "endColumn": 18}, {"ruleId": "51", "severity": 1, "message": "52", "line": 119, "column": 7, "nodeType": "53", "messageId": "54", "endLine": 119, "endColumn": 20}, {"ruleId": "51", "severity": 1, "message": "52", "line": 157, "column": 9, "nodeType": "53", "messageId": "54", "endLine": 157, "endColumn": 22}, {"ruleId": "55", "severity": 1, "message": "56", "line": 167, "column": 68, "nodeType": "57", "messageId": "58", "endLine": 167, "endColumn": 88}, {"ruleId": "51", "severity": 1, "message": "52", "line": 175, "column": 11, "nodeType": "53", "messageId": "54", "endLine": 175, "endColumn": 24}, {"ruleId": "51", "severity": 1, "message": "52", "line": 195, "column": 9, "nodeType": "53", "messageId": "54", "endLine": 195, "endColumn": 22}, {"ruleId": "59", "severity": 1, "message": "60", "line": 5, "column": 36, "nodeType": "61", "messageId": "62", "endLine": 5, "endColumn": 39, "suggestions": "63"}, {"ruleId": "59", "severity": 1, "message": "60", "line": 37, "column": 35, "nodeType": "61", "messageId": "62", "endLine": 37, "endColumn": 38, "suggestions": "64"}, {"ruleId": "59", "severity": 1, "message": "60", "line": 61, "column": 32, "nodeType": "61", "messageId": "62", "endLine": 61, "endColumn": 35, "suggestions": "65"}, "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", "@typescript-eslint/no-non-null-assertion", "Forbidden non-null assertion.", "TSNonNullExpression", "noNonNull", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["66", "67"], ["68", "69"], ["70", "71"], {"messageId": "72", "fix": "73", "desc": "74"}, {"messageId": "75", "fix": "76", "desc": "77"}, {"messageId": "72", "fix": "78", "desc": "74"}, {"messageId": "75", "fix": "79", "desc": "77"}, {"messageId": "72", "fix": "80", "desc": "74"}, {"messageId": "75", "fix": "81", "desc": "77"}, "suggestUnknown", {"range": "82", "text": "83"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "82", "text": "84"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "85", "text": "83"}, {"range": "85", "text": "84"}, {"range": "86", "text": "83"}, {"range": "86", "text": "84"}, [140, 143], "unknown", "never", [931, 934], [1479, 1482]]