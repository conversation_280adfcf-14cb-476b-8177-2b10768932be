{"version": 3, "file": "js/about.e1e6dfac.js", "mappings": "0LAIA,MAAMA,EAAa,CCHZC,MAAM,SDIPC,EAAa,CCSVD,MAAM,kBDRTE,EAAa,CCWJF,MAAM,eDVfG,EAAa,CCgBNH,MAAM,gBDfbI,EAAa,CC+BJJ,MAAM,eD7Bf,SAAUK,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAMC,GAA+BC,EAAAA,EAAAA,IAAkB,qBACjDC,GAAqBD,EAAAA,EAAAA,IAAkB,WAE7C,OAAQE,EAAAA,EAAAA,OCbRC,EAAAA,EAAAA,IA0EM,MA1ENjB,EA0EM,CD5DJQ,EAAO,KAAOA,EAAO,IAAKU,EAAAA,EAAAA,IAAmB,oGAA2GC,EAAa,+LAAyM,KCD9WC,EAAAA,EAAAA,IA4DM,MA5DNlB,EA4DM,EA3DJmB,EAAAA,EAAAA,IAmBUN,EAAA,CAnBDd,MAAM,aAAW,CACbqB,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNH,EAAAA,EAAAA,IAGM,MAHNjB,EAGM,EAFJkB,EAAAA,EAAAA,IAAwCR,EAAA,CAArBW,KAAK,gBDGxBhB,EAAO,KAAOA,EAAO,ICFrBY,EAAAA,EAAAA,IAAsB,YAAhB,aAAS,QDKnBK,SAASF,EAAAA,EAAAA,ICDT,IAUM,EAVNH,EAAAA,EAAAA,IAUM,MAVNhB,EAUM,CDPFI,EAAO,KAAOA,EAAO,ICFvBY,EAAAA,EAAAA,IAEI,KAFDnB,MAAM,eAAc,oDAEvB,KACAmB,EAAAA,EAAAA,IAIM,YDHFZ,EAAO,KAAOA,EAAO,ICAvBY,EAAAA,EAAAA,IAAkC,YAA5B,yBAAqB,KAC3BC,EAAAA,EAAAA,IAA0DR,EAAA,CAAvCW,KAAK,WAAWvB,MAAM,iBDIvCO,EAAO,KAAOA,EAAO,ICHvBY,EAAAA,EAAAA,IAAwF,KAArFM,KAAK,gCAAgCzB,MAAM,gBAAgB,0BAAsB,UDUxF0B,EAAG,KCHLN,EAAAA,EAAAA,IAoCUN,EAAA,CApCDd,MAAM,aAAW,CACbqB,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNH,EAAAA,EAAAA,IAGM,MAHNf,EAGM,EAFJgB,EAAAA,EAAAA,IAAiCR,EAAA,CAAdW,KAAK,SDMxBhB,EAAO,KAAOA,EAAO,ICLrBY,EAAAA,EAAAA,IAAiB,YAAX,QAAI,QDQdK,SAASF,EAAAA,EAAAA,ICJT,IA2BM,CDtBJf,EAAO,KAAOA,EAAO,ICLvBY,EAAAA,EAAAA,IA2BM,OA3BDnB,MAAM,aAAW,EACpBmB,EAAAA,EAAAA,IAyBM,OAzBDnB,MAAM,aAAW,EACpBmB,EAAAA,EAAAA,IAGM,OAHDnB,MAAM,aAAW,EACpBmB,EAAAA,EAAAA,IAAqC,QAA/BnB,MAAM,cAAa,UACzBmB,EAAAA,EAAAA,IAAkD,QAA5CnB,MAAM,cAAa,yBAE3BmB,EAAAA,EAAAA,IAGM,OAHDnB,MAAM,aAAW,EACpBmB,EAAAA,EAAAA,IAAsC,QAAhCnB,MAAM,cAAa,WACzBmB,EAAAA,EAAAA,IAA4C,QAAtCnB,MAAM,cAAa,mBAE3BmB,EAAAA,EAAAA,IAGM,OAHDnB,MAAM,aAAW,EACpBmB,EAAAA,EAAAA,IAAoC,QAA9BnB,MAAM,cAAa,SACzBmB,EAAAA,EAAAA,IAA2C,QAArCnB,MAAM,cAAa,kBAE3BmB,EAAAA,EAAAA,IAGM,OAHDnB,MAAM,aAAW,EACpBmB,EAAAA,EAAAA,IAAqC,QAA/BnB,MAAM,cAAa,UACzBmB,EAAAA,EAAAA,IAA8C,QAAxCnB,MAAM,cAAa,qBAE3BmB,EAAAA,EAAAA,IAGM,OAHDnB,MAAM,aAAW,EACpBmB,EAAAA,EAAAA,IAAqC,QAA/BnB,MAAM,cAAa,UACzBmB,EAAAA,EAAAA,IAA8C,QAAxCnB,MAAM,cAAa,qBAE3BmB,EAAAA,EAAAA,IAGM,OAHDnB,MAAM,aAAW,EACpBmB,EAAAA,EAAAA,IAAqC,QAA/BnB,MAAM,cAAa,UACzBmB,EAAAA,EAAAA,IAAiD,QAA3CnB,MAAM,cAAa,2BDQzB,MAEN0B,EAAG,EACHC,GAAI,CAAC,QAIb,C,aCFA,GAAeC,EAAAA,EAAAA,IAAgB,CAC7BC,KAAM,YACNC,WAAY,CACVC,gBAAeA,EAAAA,M,UC9EnB,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS3B,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://tab-kit-web/./src/views/AboutView.vue?ceb7", "webpack://tab-kit-web/./src/views/AboutView.vue", "webpack://tab-kit-web/./src/views/AboutView.vue?d56f"], "sourcesContent": ["import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createStaticVNode as _createStaticVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\"\nimport _imports_0 from '@/assets/logo.svg'\n\n\nconst _hoisted_1 = { class: \"about\" }\nconst _hoisted_2 = { class: \"app-info-cards\" }\nconst _hoisted_3 = { class: \"card-header\" }\nconst _hoisted_4 = { class: \"info-content\" }\nconst _hoisted_5 = { class: \"card-header\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\")!\n  const _component_el_card = _resolveComponent(\"el-card\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _cache[6] || (_cache[6] = _createStaticVNode(\"<div class=\\\"page-header\\\" data-v-769f0fd8><div class=\\\"app-logo-section\\\" data-v-769f0fd8><img src=\\\"\" + _imports_0 + \"\\\" alt=\\\"TabKit Logo\\\" class=\\\"logo-icon\\\" data-v-769f0fd8><div class=\\\"app-info\\\" data-v-769f0fd8><h1 data-v-769f0fd8>TabKit</h1><p class=\\\"version\\\" data-v-769f0fd8>版本 1.0.0</p></div></div></div>\", 1)),\n    _createElementVNode(\"div\", _hoisted_2, [\n      _createVNode(_component_el_card, { class: \"info-card\" }, {\n        header: _withCtx(() => [\n          _createElementVNode(\"div\", _hoisted_3, [\n            _createVNode(_component_font_awesome_icon, { icon: \"info-circle\" }),\n            _cache[0] || (_cache[0] = _createElementVNode(\"span\", null, \"关于 TabKit\", -1))\n          ])\n        ]),\n        default: _withCtx(() => [\n          _createElementVNode(\"div\", _hoisted_4, [\n            _cache[3] || (_cache[3] = _createElementVNode(\"p\", { class: \"description\" }, \" Alsi.Tab.Kit 是一个集成多种工具的桌面应用程序，旨在提高工作效率和简化日常任务。 \", -1)),\n            _createElementVNode(\"div\", null, [\n              _cache[1] || (_cache[1] = _createElementVNode(\"span\", null, \"使用过程中遇到问题或有任何建议，欢迎联系：\", -1)),\n              _createVNode(_component_font_awesome_icon, {\n                icon: \"envelope\",\n                class: \"contact-icon\"\n              }),\n              _cache[2] || (_cache[2] = _createElementVNode(\"a\", {\n                href: \"mailto:<EMAIL>\",\n                class: \"contact-link\"\n              }, \"<EMAIL>\", -1))\n            ])\n          ])\n        ]),\n        _: 1\n      }),\n      _createVNode(_component_el_card, { class: \"info-card\" }, {\n        header: _withCtx(() => [\n          _createElementVNode(\"div\", _hoisted_5, [\n            _createVNode(_component_font_awesome_icon, { icon: \"code\" }),\n            _cache[4] || (_cache[4] = _createElementVNode(\"span\", null, \"技术信息\", -1))\n          ])\n        ]),\n        default: _withCtx(() => [\n          _cache[5] || (_cache[5] = _createElementVNode(\"div\", { class: \"tech-info\" }, [\n            _createElementVNode(\"div\", { class: \"tech-grid\" }, [\n              _createElementVNode(\"div\", { class: \"tech-item\" }, [\n                _createElementVNode(\"span\", { class: \"tech-label\" }, \"前端框架:\"),\n                _createElementVNode(\"span\", { class: \"tech-value\" }, \"Vue 3 + TypeScript\")\n              ]),\n              _createElementVNode(\"div\", { class: \"tech-item\" }, [\n                _createElementVNode(\"span\", { class: \"tech-label\" }, \"UI组件库:\"),\n                _createElementVNode(\"span\", { class: \"tech-value\" }, \"Element Plus\")\n              ]),\n              _createElementVNode(\"div\", { class: \"tech-item\" }, [\n                _createElementVNode(\"span\", { class: \"tech-label\" }, \"图标库:\"),\n                _createElementVNode(\"span\", { class: \"tech-value\" }, \"FontAwesome\")\n              ]),\n              _createElementVNode(\"div\", { class: \"tech-item\" }, [\n                _createElementVNode(\"span\", { class: \"tech-label\" }, \"后端框架:\"),\n                _createElementVNode(\"span\", { class: \"tech-value\" }, \".NET Framework\")\n              ]),\n              _createElementVNode(\"div\", { class: \"tech-item\" }, [\n                _createElementVNode(\"span\", { class: \"tech-label\" }, \"桌面框架:\"),\n                _createElementVNode(\"span\", { class: \"tech-value\" }, \"WPF + WebView2\")\n              ]),\n              _createElementVNode(\"div\", { class: \"tech-item\" }, [\n                _createElementVNode(\"span\", { class: \"tech-label\" }, \"构建工具:\"),\n                _createElementVNode(\"span\", { class: \"tech-value\" }, \"Vue CLI + Webpack\")\n              ])\n            ])\n          ], -1))\n        ]),\n        _: 1,\n        __: [5]\n      })\n    ])\n  ]))\n}", "<template>\n  <div class=\"about\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <div class=\"app-logo-section\">\n        <img src=\"@/assets/logo.svg\" alt=\"TabKit Logo\" class=\"logo-icon\" />\n        <div class=\"app-info\">\n          <h1>TabKit</h1>\n          <p class=\"version\">版本 1.0.0</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- 应用信息 -->\n    <div class=\"app-info-cards\">\n      <el-card class=\"info-card\">\n        <template #header>\n          <div class=\"card-header\">\n            <font-awesome-icon icon=\"info-circle\" />\n            <span>关于 TabKit</span>\n          </div>\n        </template>\n\n        <div class=\"info-content\">\n          <p class=\"description\">\n            Alsi.Tab.Kit 是一个集成多种工具的桌面应用程序，旨在提高工作效率和简化日常任务。\n          </p>\n          <div>\n            <span>使用过程中遇到问题或有任何建议，欢迎联系：</span>\n            <font-awesome-icon icon=\"envelope\" class=\"contact-icon\" />\n            <a href=\"mailto:<EMAIL>\" class=\"contact-link\" ><EMAIL></a>\n          </div>\n\n        </div>\n      </el-card>\n\n      <!-- 技术信息 -->\n      <el-card class=\"info-card\">\n        <template #header>\n          <div class=\"card-header\">\n            <font-awesome-icon icon=\"code\" />\n            <span>技术信息</span>\n          </div>\n        </template>\n\n        <div class=\"tech-info\">\n          <div class=\"tech-grid\">\n            <div class=\"tech-item\">\n              <span class=\"tech-label\">前端框架:</span>\n              <span class=\"tech-value\">Vue 3 + TypeScript</span>\n            </div>\n            <div class=\"tech-item\">\n              <span class=\"tech-label\">UI组件库:</span>\n              <span class=\"tech-value\">Element Plus</span>\n            </div>\n            <div class=\"tech-item\">\n              <span class=\"tech-label\">图标库:</span>\n              <span class=\"tech-value\">FontAwesome</span>\n            </div>\n            <div class=\"tech-item\">\n              <span class=\"tech-label\">后端框架:</span>\n              <span class=\"tech-value\">.NET Framework</span>\n            </div>\n            <div class=\"tech-item\">\n              <span class=\"tech-label\">桌面框架:</span>\n              <span class=\"tech-value\">WPF + WebView2</span>\n            </div>\n            <div class=\"tech-item\">\n              <span class=\"tech-label\">构建工具:</span>\n              <span class=\"tech-value\">Vue CLI + Webpack</span>\n            </div>\n          </div>\n        </div>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent } from \"vue\";\nimport { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';\n\nexport default defineComponent({\n  name: \"AboutView\",\n  components: {\n    FontAwesomeIcon,\n  },\n});\n</script>\n\n<style scoped>\n.about {\n  padding: 20px;\n  max-width: 1000px;\n  margin: 0 auto;\n}\n\n.page-header {\n  margin-bottom: 40px;\n}\n\n.app-logo-section {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  justify-content: center;\n}\n\n.logo-icon {\n  width: 4rem;\n  height: 4rem;\n  flex-shrink: 0;\n}\n\n.app-info {\n  text-align: left;\n}\n\n.app-info h1 {\n  font-size: 2.5rem;\n  color: #2c3e50;\n  margin: 0;\n}\n\n.version {\n  color: #7f8c8d;\n  font-size: 1.1rem;\n  margin: 0;\n}\n\n.app-info-cards {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  margin-bottom: 40px;\n}\n\n.info-card {\n  border-radius: 8px;\n}\n\n.card-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: bold;\n  color: #2c3e50;\n  font-size: 1.1rem;\n}\n\n.info-content {\n  line-height: 1.6;\n}\n\n.description {\n  color: #555;\n  font-size: 1rem;\n  margin-bottom: 25px;\n}\n\n.features h3 {\n  color: #2c3e50;\n  margin-bottom: 15px;\n  font-size: 1.2rem;\n}\n\n.features ul {\n  list-style: none;\n  padding: 0;\n}\n\n.features li {\n  display: flex;\n  align-items: flex-start;\n  gap: 10px;\n  margin-bottom: 15px;\n  padding: 10px;\n  background-color: #f8f9fa;\n  border-radius: 6px;\n}\n\n.feature-icon {\n  color: #3498db;\n  margin-top: 2px;\n  flex-shrink: 0;\n}\n\n.features li strong {\n  color: #2c3e50;\n}\n\n.tech-info {\n  padding: 10px 0;\n}\n\n.tech-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 15px;\n}\n\n.tech-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px;\n  background-color: #f8f9fa;\n  border-radius: 6px;\n}\n\n.tech-label {\n  color: #7f8c8d;\n  font-weight: 500;\n}\n\n.tech-value {\n  color: #2c3e50;\n  font-weight: bold;\n}\n\n.contact-icon {\n  color: var(--el-color-primary);\n  width: 16px;\n}\n\n.contact-link {\n  color: var(--el-color-primary);\n  text-decoration: none;\n  transition: color 0.3s ease;\n  margin: 0 0 0 6px;\n}\n\n.contact-link:hover {\n  color: var(--el-color-primary-dark-1);\n  text-decoration: underline;\n}\n\n\n\n@media (max-width: 768px) {\n  .about {\n    padding: 15px;\n  }\n\n  .app-logo-section {\n    flex-direction: column;\n    gap: 15px;\n  }\n\n  .app-info {\n    text-align: center;\n  }\n\n  .app-info h1 {\n    font-size: 2rem;\n  }\n\n  .logo-icon {\n    width: 3rem;\n    height: 3rem;\n  }\n\n  .tech-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .tech-item {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 5px;\n  }\n\n  .features li {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n  }\n}\n</style>\n", "import { render } from \"./AboutView.vue?vue&type=template&id=769f0fd8&scoped=true&ts=true\"\nimport script from \"./AboutView.vue?vue&type=script&lang=ts\"\nexport * from \"./AboutView.vue?vue&type=script&lang=ts\"\n\nimport \"./AboutView.vue?vue&type=style&index=0&id=769f0fd8&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-769f0fd8\"]])\n\nexport default __exports__"], "names": ["_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "_component_font_awesome_icon", "_resolveComponent", "_component_el_card", "_openBlock", "_createElementBlock", "_createStaticVNode", "_imports_0", "_createElementVNode", "_createVNode", "header", "_withCtx", "icon", "default", "href", "_", "__", "defineComponent", "name", "components", "FontAwesomeIcon", "__exports__"], "sourceRoot": ""}